/**
 * AI Memory system for tracking player behavior and adapting AI responses
 */

export class AIMemory {
    constructor() {
        // Initialize memory
        this.reset();
    }
    
    /**
     * Reset memory to default state
     */
    reset() {
        this.playerActions = {
            attacks: [],        // Recent player attacks
            defenses: [],       // Recent player defenses
            movements: [],      // Recent player movements
            lastPosition: null, // Last known player position
        };
        
        this.playerPatterns = {
            preferredAttacks: null,    // Most common attack type
            preferredDefense: null,    // Most common defense type
            movementStyle: null,       // Movement pattern (aggressive, cautious, etc.)
            preferredRange: null,      // Preferred fighting distance
            attackFrequency: null,     // How often player attacks
            isAggressive: null,        // Whether player is aggressive
            isPredictable: null,       // Whether player is predictable
        };
        
        this.encounterHistory = {
            count: 0,               // Number of encounters
            lastEncounterTime: 0,   // Time of last encounter
            damageDealt: 0,         // Damage dealt to player
            damageTaken: 0,         // Damage taken from player
            killCount: 0,           // Number of times killed player
            deathCount: 0,          // Number of times killed by player
        };
        
        // Memory decay parameters
        this.shortTermMemoryDuration = 10000; // 10 seconds in ms
        this.longTermMemoryThreshold = 3;     // Number of encounters to form long-term memory
    }
    
    /**
     * Record a player attack
     * @param {Object} attack - Attack data
     * @param {String} attack.type - Type of attack (melee, ranged, etc.)
     * @param {Number} attack.damage - Damage dealt
     * @param {Boolean} attack.hit - Whether the attack hit
     */
    recordPlayerAttack(attack) {
        this.playerActions.attacks.push({
            ...attack,
            timestamp: Date.now()
        });
        
        // Keep only recent attacks (short-term memory)
        this._pruneShortTermMemory(this.playerActions.attacks);
        
        // Update player patterns
        this._updateAttackPatterns();
    }
    
    /**
     * Record a player defense
     * @param {Object} defense - Defense data
     * @param {String} defense.type - Type of defense (block, dodge, etc.)
     * @param {Boolean} defense.successful - Whether the defense was successful
     */
    recordPlayerDefense(defense) {
        this.playerActions.defenses.push({
            ...defense,
            timestamp: Date.now()
        });
        
        // Keep only recent defenses (short-term memory)
        this._pruneShortTermMemory(this.playerActions.defenses);
        
        // Update player patterns
        this._updateDefensePatterns();
    }
    
    /**
     * Record player movement
     * @param {Object} movement - Movement data
     * @param {THREE.Vector3} movement.position - Player position
     * @param {THREE.Vector3} movement.velocity - Player velocity
     * @param {Number} movement.distanceToEnemy - Distance to enemy
     */
    recordPlayerMovement(movement) {
        this.playerActions.movements.push({
            ...movement,
            timestamp: Date.now()
        });
        
        // Store last position
        this.playerActions.lastPosition = movement.position.clone();
        
        // Keep only recent movements (short-term memory)
        this._pruneShortTermMemory(this.playerActions.movements);
        
        // Update player patterns
        this._updateMovementPatterns();
    }
    
    /**
     * Record an encounter with the player
     * @param {Object} encounter - Encounter data
     * @param {Number} encounter.damageDealt - Damage dealt to player
     * @param {Number} encounter.damageTaken - Damage taken from player
     * @param {Boolean} encounter.playerKilled - Whether player was killed
     * @param {Boolean} encounter.enemyKilled - Whether enemy was killed
     */
    recordEncounter(encounter) {
        this.encounterHistory.count++;
        this.encounterHistory.lastEncounterTime = Date.now();
        
        if (encounter.damageDealt) this.encounterHistory.damageDealt += encounter.damageDealt;
        if (encounter.damageTaken) this.encounterHistory.damageTaken += encounter.damageTaken;
        if (encounter.playerKilled) this.encounterHistory.killCount++;
        if (encounter.enemyKilled) this.encounterHistory.deathCount++;
    }
    
    /**
     * Get adaptive response based on player patterns
     * @returns {Object} - Adaptive response data
     */
    getAdaptiveResponse() {
        return {
            // Attack adaptations
            shouldCounterAttack: this.playerPatterns.preferredAttacks === 'melee',
            shouldKeepDistance: this.playerPatterns.preferredAttacks === 'ranged',
            shouldBeUnpredictable: this.playerPatterns.isPredictable === false,
            
            // Defense adaptations
            shouldFakeAttacks: this.playerPatterns.preferredDefense === 'block',
            shouldAttackQuickly: this.playerPatterns.preferredDefense === 'dodge',
            
            // Movement adaptations
            preferredEngagementDistance: this._getPreferredEngagementDistance(),
            shouldBeAggressive: this.playerPatterns.isAggressive === false,
            shouldBeCautious: this.playerPatterns.isAggressive === true,
            
            // General adaptations
            difficultyMultiplier: this._calculateDifficultyMultiplier(),
        };
    }
    
    /**
     * Check if memory is fresh (recent encounter)
     * @returns {Boolean} - Whether memory is fresh
     */
    isMemoryFresh() {
        const now = Date.now();
        return (now - this.encounterHistory.lastEncounterTime) < this.shortTermMemoryDuration;
    }
    
    /**
     * Check if long-term memory has formed
     * @returns {Boolean} - Whether long-term memory has formed
     */
    hasLongTermMemory() {
        return this.encounterHistory.count >= this.longTermMemoryThreshold;
    }
    
    /**
     * Prune short-term memory to keep only recent items
     * @param {Array} memoryArray - Array of memory items
     * @private
     */
    _pruneShortTermMemory(memoryArray) {
        const now = Date.now();
        const cutoffTime = now - this.shortTermMemoryDuration;
        
        // Remove old memories
        let i = 0;
        while (i < memoryArray.length) {
            if (memoryArray[i].timestamp < cutoffTime) {
                memoryArray.splice(i, 1);
            } else {
                i++;
            }
        }
        
        // Keep at most 10 items
        if (memoryArray.length > 10) {
            memoryArray.splice(0, memoryArray.length - 10);
        }
    }
    
    /**
     * Update attack patterns based on recorded attacks
     * @private
     */
    _updateAttackPatterns() {
        if (this.playerActions.attacks.length === 0) return;
        
        // Count attack types
        const attackCounts = {};
        for (const attack of this.playerActions.attacks) {
            attackCounts[attack.type] = (attackCounts[attack.type] || 0) + 1;
        }
        
        // Find most common attack type
        let maxCount = 0;
        let preferredAttack = null;
        for (const type in attackCounts) {
            if (attackCounts[type] > maxCount) {
                maxCount = attackCounts[type];
                preferredAttack = type;
            }
        }
        
        this.playerPatterns.preferredAttacks = preferredAttack;
        
        // Calculate attack frequency
        const timeSpan = this.playerActions.attacks[this.playerActions.attacks.length - 1].timestamp - 
                         this.playerActions.attacks[0].timestamp;
        if (timeSpan > 0) {
            this.playerPatterns.attackFrequency = this.playerActions.attacks.length / (timeSpan / 1000);
        }
        
        // Determine if player is predictable
        // If more than 70% of attacks are the same type, player is predictable
        this.playerPatterns.isPredictable = maxCount / this.playerActions.attacks.length > 0.7;
    }
    
    /**
     * Update defense patterns based on recorded defenses
     * @private
     */
    _updateDefensePatterns() {
        if (this.playerActions.defenses.length === 0) return;
        
        // Count defense types
        const defenseCounts = {};
        for (const defense of this.playerActions.defenses) {
            defenseCounts[defense.type] = (defenseCounts[defense.type] || 0) + 1;
        }
        
        // Find most common defense type
        let maxCount = 0;
        let preferredDefense = null;
        for (const type in defenseCounts) {
            if (defenseCounts[type] > maxCount) {
                maxCount = defenseCounts[type];
                preferredDefense = type;
            }
        }
        
        this.playerPatterns.preferredDefense = preferredDefense;
    }
    
    /**
     * Update movement patterns based on recorded movements
     * @private
     */
    _updateMovementPatterns() {
        if (this.playerActions.movements.length < 2) return;
        
        // Calculate average distance to enemy
        let totalDistance = 0;
        for (const movement of this.playerActions.movements) {
            totalDistance += movement.distanceToEnemy;
        }
        const avgDistance = totalDistance / this.playerActions.movements.length;
        this.playerPatterns.preferredRange = avgDistance;
        
        // Determine if player is aggressive
        // If player tends to stay close, they're aggressive
        this.playerPatterns.isAggressive = avgDistance < 5.0; // Arbitrary threshold
        
        // Determine movement style
        // Calculate how much the player moves
        let totalMovement = 0;
        for (let i = 1; i < this.playerActions.movements.length; i++) {
            const prev = this.playerActions.movements[i - 1];
            const curr = this.playerActions.movements[i];
            
            // Calculate distance moved
            const moveDist = curr.position.distanceTo(prev.position);
            totalMovement += moveDist;
        }
        
        const avgMovement = totalMovement / (this.playerActions.movements.length - 1);
        
        // Classify movement style
        if (avgMovement < 0.5) {
            this.playerPatterns.movementStyle = 'stationary';
        } else if (avgMovement < 2.0) {
            this.playerPatterns.movementStyle = 'cautious';
        } else {
            this.playerPatterns.movementStyle = 'mobile';
        }
    }
    
    /**
     * Calculate preferred engagement distance based on patterns
     * @returns {Number} - Preferred distance
     * @private
     */
    _getPreferredEngagementDistance() {
        // If we have a preferred range from movement patterns, use that
        if (this.playerPatterns.preferredRange !== null) {
            // Invert the player's preference to counter them
            if (this.playerPatterns.preferredRange < 5.0) {
                return 8.0; // Stay far if player likes to be close
            } else {
                return 3.0; // Get close if player likes to keep distance
            }
        }
        
        // Otherwise, use attack type as a hint
        if (this.playerPatterns.preferredAttacks === 'melee') {
            return 7.0; // Stay away from melee attackers
        } else if (this.playerPatterns.preferredAttacks === 'ranged') {
            return 3.0; // Get close to ranged attackers
        }
        
        // Default
        return 5.0;
    }
    
    /**
     * Calculate difficulty multiplier based on encounter history
     * @returns {Number} - Difficulty multiplier
     * @private
     */
    _calculateDifficultyMultiplier() {
        // If no encounters yet, use default
        if (this.encounterHistory.count === 0) return 1.0;
        
        // Calculate win/loss ratio
        const winRatio = this.encounterHistory.killCount / 
                        (this.encounterHistory.deathCount || 1); // Avoid division by zero
        
        // Calculate damage ratio
        const damageRatio = this.encounterHistory.damageDealt / 
                           (this.encounterHistory.damageTaken || 1); // Avoid division by zero
        
        // Combine ratios (higher means AI is doing well)
        const combinedRatio = (winRatio + damageRatio) / 2;
        
        // Scale difficulty based on combined ratio
        // If AI is doing well (high ratio), reduce difficulty
        // If AI is doing poorly (low ratio), increase difficulty
        if (combinedRatio > 2.0) {
            return 0.8; // Reduce difficulty if AI is dominating
        } else if (combinedRatio < 0.5) {
            return 1.2; // Increase difficulty if AI is struggling
        } else {
            return 1.0; // Keep same difficulty if balanced
        }
    }
}
