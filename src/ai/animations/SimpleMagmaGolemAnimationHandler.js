/**
 * SimpleMagmaGolemAnimationHandler.js
 *
 * A simplified animation handler for magma golem enemies.
 * This handler uses simple, direct rotations rather than complex calculations.
 */

import * as THREE from 'three';
import { AIStates } from '../AIStates.js';

export class SimpleMagmaGolemAnimationHandler {
    /**
     * Create a magma golem animation handler
     * @param {THREE.Group} golemModel - The magma golem model to animate
     */
    constructor(golemModel) {
        this.golemModel = golemModel;

        // Get body parts - search directly in the model
        this.bodyGroup = golemModel.getObjectByName('body') || golemModel.getObjectByName('core');
        this.headGroup = golemModel.getObjectByName('head');
        this.leftArmGroup = golemModel.getObjectByName('leftArm');
        this.rightArmGroup = golemModel.getObjectByName('rightArm');
        this.leftLegGroup = golemModel.getObjectByName('leftLeg');
        this.rightLegGroup = golemModel.getObjectByName('rightLeg');

        // Store initial transforms
        this.initialTransforms = {
            model: {
                position: golemModel.position.clone(),
                rotation: golemModel.rotation.clone(),
                quaternion: golemModel.quaternion.clone(),
                scale: golemModel.scale.clone()
            },
            body: this.bodyGroup ? {
                position: this.bodyGroup.position.clone(),
                rotation: this.bodyGroup.rotation.clone(),
                quaternion: this.bodyGroup.quaternion.clone()
            } : null,
            head: this.headGroup ? {
                position: this.headGroup.position.clone(),
                rotation: this.headGroup.rotation.clone(),
                quaternion: this.headGroup.quaternion.clone()
            } : null,
            leftArm: this.leftArmGroup ? {
                position: this.leftArmGroup.position.clone(),
                rotation: this.leftArmGroup.rotation.clone(),
                quaternion: this.leftArmGroup.quaternion.clone()
            } : null,
            rightArm: this.rightArmGroup ? {
                position: this.rightArmGroup.position.clone(),
                rotation: this.rightArmGroup.rotation.clone(),
                quaternion: this.rightArmGroup.quaternion.clone()
            } : null,
            leftLeg: this.leftLegGroup ? {
                position: this.leftLegGroup.position.clone(),
                rotation: this.leftLegGroup.rotation.clone(),
                quaternion: this.leftLegGroup.quaternion.clone()
            } : null,
            rightLeg: this.rightLegGroup ? {
                position: this.rightLegGroup.position.clone(),
                rotation: this.rightLegGroup.rotation.clone(),
                quaternion: this.rightLegGroup.quaternion.clone()
            } : null
        };

        // Debug output to check if all parts are found
        console.log('SimpleMagmaGolemAnimationHandler initialized with parts:', {
            body: !!this.bodyGroup,
            head: !!this.headGroup,
            leftArm: !!this.leftArmGroup,
            rightArm: !!this.rightArmGroup,
            leftLeg: !!this.leftLegGroup,
            rightLeg: !!this.rightLegGroup
        });

        // Get animation data from model or use defaults
        this.animationData = golemModel.userData.animationData || {
            walkSpeed: 1.2,              // Walk speed multiplier
            walkAmplitude: Math.PI / 8,  // Amplitude for leg movement
            armSwingAmplitude: Math.PI / 6, // Amplitude for arm swing
            attackDuration: 1.2,         // Duration of attack animation in seconds
            idleSpeed: 0.8               // Speed of idle animation
        };

        // Animation state
        this.currentState = AIStates.IDLE;
        this.attackProgress = 0;
        this.lastHitTime = 0;
        this.animationTime = 0;

        // Store original positions and rotations
        this.storeOriginalTransforms();

        // Store base position for reference (but don't modify it directly)
        if (golemModel) {
            golemModel.userData.basePositionY = golemModel.position.y;
            golemModel.userData.basePositionZ = golemModel.position.z;
        }
    }

    /**
     * Store original positions and rotations of all body parts
     */
    storeOriginalTransforms() {
        this.originalPositions = {};
        this.originalRotations = {};

        const groups = [
            'golemModel', 'bodyGroup', 'headGroup',
            'leftArmGroup', 'rightArmGroup', 'leftLegGroup', 'rightLegGroup'
        ];

        for (const groupName of groups) {
            const group = this[groupName];
            if (group) {
                this.originalPositions[groupName] = group.position.clone();
                this.originalRotations[groupName] = group.rotation.clone();
            }
        }
    }

    /**
     * Reset all body parts to original positions
     */
    resetTransforms() {
        const groups = [
            'golemModel', 'bodyGroup', 'headGroup',
            'leftArmGroup', 'rightArmGroup', 'leftLegGroup', 'rightLegGroup'
        ];

        for (const groupName of groups) {
            const group = this[groupName];
            if (group) {
                if (this.originalPositions[groupName]) {
                    group.position.copy(this.originalPositions[groupName]);
                }
                if (this.originalRotations[groupName]) {
                    group.rotation.copy(this.originalRotations[groupName]);
                }
            }
        }
    }

    /**
     * Reset only child part transforms, leaving root model untouched
     * Used during hit/knockback to avoid fighting with physics
     */
    safeResetChildTransforms() {
        const childGroups = [
            'bodyGroup', 'headGroup',
            'leftArmGroup', 'rightArmGroup', 'leftLegGroup', 'rightLegGroup'
        ];

        for (const groupName of childGroups) {
            const group = this[groupName];
            if (group) {
                if (this.originalPositions[groupName]) {
                    group.position.copy(this.originalPositions[groupName]);
                }
                if (this.originalRotations[groupName]) {
                    group.rotation.copy(this.originalRotations[groupName]);
                }
            }
        }
    }

    /**
     * Update animations based on state and time
     * @param {string} state - Current AI state
     * @param {number} deltaTime - Time since last update
     * @param {number} globalTime - Global time for continuous animations
     */
    update(state, deltaTime, globalTime) {
        if (!this.golemModel) return;

        // CRITICAL: Check if we should freeze all animations
        const isRotationFrozen = this.golemModel.userData?.freezeRotation === true;
        const isHitOrKnockback = (state === AIStates.HIT_REACTING || state === AIStates.KNOCKBACK);
        const shouldFreeze = isRotationFrozen || isHitOrKnockback;

        if (shouldFreeze) {
            // If we're entering frozen state, store current transforms
            if (!this.golemModel.userData.frozenState) {
                this.golemModel.userData.frozenState = {
                    position: this.golemModel.position.clone(),
                    rotation: this.golemModel.rotation.clone(),
                    quaternion: this.golemModel.quaternion.clone(),
                    scale: this.golemModel.scale.clone(),
                    body: this.bodyGroup ? {
                        position: this.bodyGroup.position.clone(),
                        rotation: this.bodyGroup.rotation.clone(),
                        quaternion: this.bodyGroup.quaternion.clone()
                    } : null,
                    head: this.headGroup ? {
                        position: this.headGroup.position.clone(),
                        rotation: this.headGroup.rotation.clone(),
                        quaternion: this.headGroup.quaternion.clone()
                    } : null,
                    leftArm: this.leftArmGroup ? {
                        position: this.leftArmGroup.position.clone(),
                        rotation: this.leftArmGroup.rotation.clone(),
                        quaternion: this.leftArmGroup.quaternion.clone()
                    } : null,
                    rightArm: this.rightArmGroup ? {
                        position: this.rightArmGroup.position.clone(),
                        rotation: this.rightArmGroup.rotation.clone(),
                        quaternion: this.rightArmGroup.quaternion.clone()
                    } : null,
                    leftLeg: this.leftLegGroup ? {
                        position: this.leftLegGroup.position.clone(),
                        rotation: this.leftLegGroup.rotation.clone(),
                        quaternion: this.leftLegGroup.quaternion.clone()
                    } : null,
                    rightLeg: this.rightLegGroup ? {
                        position: this.rightLegGroup.position.clone(),
                        rotation: this.rightLegGroup.rotation.clone(),
                        quaternion: this.rightLegGroup.quaternion.clone()
                    } : null
                };
            }

            // Restore frozen state if it exists, otherwise use initial transforms
            const transforms = this.golemModel.userData.frozenState || this.initialTransforms;

            // Apply transforms
            this.golemModel.position.copy(transforms.model.position);
            this.golemModel.quaternion.copy(transforms.model.quaternion);
            this.golemModel.rotation.copy(transforms.model.rotation);
            this.golemModel.scale.copy(transforms.model.scale);

            if (this.bodyGroup && transforms.body) {
                this.bodyGroup.position.copy(transforms.body.position);
                this.bodyGroup.quaternion.copy(transforms.body.quaternion);
                this.bodyGroup.rotation.copy(transforms.body.rotation);
            }
            if (this.headGroup && transforms.head) {
                this.headGroup.position.copy(transforms.head.position);
                this.headGroup.quaternion.copy(transforms.head.quaternion);
                this.headGroup.rotation.copy(transforms.head.rotation);
            }
            if (this.leftArmGroup && transforms.leftArm) {
                this.leftArmGroup.position.copy(transforms.leftArm.position);
                this.leftArmGroup.quaternion.copy(transforms.leftArm.quaternion);
                this.leftArmGroup.rotation.copy(transforms.leftArm.rotation);
            }
            if (this.rightArmGroup && transforms.rightArm) {
                this.rightArmGroup.position.copy(transforms.rightArm.position);
                this.rightArmGroup.quaternion.copy(transforms.rightArm.quaternion);
                this.rightArmGroup.rotation.copy(transforms.rightArm.rotation);
            }
            if (this.leftLegGroup && transforms.leftLeg) {
                this.leftLegGroup.position.copy(transforms.leftLeg.position);
                this.leftLegGroup.quaternion.copy(transforms.leftLeg.quaternion);
                this.leftLegGroup.rotation.copy(transforms.leftLeg.rotation);
            }
            if (this.rightLegGroup && transforms.rightLeg) {
                this.rightLegGroup.position.copy(transforms.rightLeg.position);
                this.rightLegGroup.quaternion.copy(transforms.rightLeg.quaternion);
                this.rightLegGroup.rotation.copy(transforms.rightLeg.rotation);
            }

            // Force updates
            this.golemModel.updateMatrix();
            this.golemModel.updateMatrixWorld(true);

            // Skip all further animation
            return;
        } else {
            // Clear frozen state when unfreezing
            this.golemModel.userData.frozenState = null;
        }

        // For non-frozen states, proceed with normal animation
        this.safeResetChildTransforms();

        // Update animation time
        this.animationTime += deltaTime;

        // Handle state transition
        if (this.currentState !== state) {
            this._storePose();
            this.previousState = this.currentState;
            this.currentState = state;
            this.transitionProgress = 0;

            if (state === AIStates.ATTACKING) {
                this.isAttacking = true;
                this.attackAnimationProgress = 0;
            } else if (this.previousState === AIStates.ATTACKING) {
                this.isAttacking = false;
                this.attackAnimationProgress = 0;
            } else if (state === AIStates.HIT_REACTING || state === AIStates.KNOCKBACK) {
                this.lastHitTime = globalTime;

                // CRITICAL: Store world matrix when entering frozen state
                this.golemModel.userData.frozenWorldMatrix = this.golemModel.matrixWorld.clone();

                // CRITICAL: Store child part transforms when entering hit/knockback state
                this.golemModel.userData.frozenChildTransforms = {
                    body: this.bodyGroup ? {
                        position: this.bodyGroup.position.clone(),
                        rotation: this.bodyGroup.rotation.clone()
                    } : null,
                    head: this.headGroup ? {
                        position: this.headGroup.position.clone(),
                        rotation: this.headGroup.rotation.clone()
                    } : null,
                    leftArm: this.leftArmGroup ? {
                        position: this.leftArmGroup.position.clone(),
                        rotation: this.leftArmGroup.rotation.clone()
                    } : null,
                    rightArm: this.rightArmGroup ? {
                        position: this.rightArmGroup.position.clone(),
                        rotation: this.rightArmGroup.rotation.clone()
                    } : null,
                    leftLeg: this.leftLegGroup ? {
                        position: this.leftLegGroup.position.clone(),
                        rotation: this.leftLegGroup.rotation.clone()
                    } : null,
                    rightLeg: this.rightLegGroup ? {
                        position: this.rightLegGroup.position.clone(),
                        rotation: this.rightLegGroup.rotation.clone()
                    } : null
                };

                console.log(`[SimpleMagmaGolemAnimationHandler] Hit reaction at time ${globalTime}`);
            }
        }

        // Update attack progress
        if (state === AIStates.ATTACKING) {
            this.attackProgress += deltaTime / this.animationData.attackDuration;
            if (this.attackProgress >= 1.0) {
                this.attackProgress = 0;
            }
        }

        // Apply animations to child parts
        switch (state) {
            case AIStates.IDLE:
                this.applyIdleAnimation(globalTime);
                break;
            case AIStates.MOVING:
            case AIStates.STRAFING:
            case AIStates.FLEEING:
                this.applyWalkingAnimation(globalTime);
                break;
            case AIStates.ATTACKING:
                this.applyAttackingAnimation(globalTime);
                break;
            case AIStates.HIT_REACTING:
            case AIStates.KNOCKBACK:
                this.applyHitReactionAnimation(globalTime);
                break;
            default:
                this.applyIdleAnimation(globalTime);
                break;
        }

        // Handle root transform for non-frozen states
        if (state === AIStates.HIT_REACTING || state === AIStates.KNOCKBACK) {
            // During hit/knockback, preserve physics transform
            this.golemModel.position.copy(this.golemModel.userData.frozenState.position);
            this.golemModel.quaternion.copy(this.golemModel.userData.frozenState.quaternion);
            this.golemModel.rotation.copy(this.golemModel.userData.frozenState.rotation);
            this.golemModel.scale.copy(this.golemModel.userData.frozenState.scale);
        } else {
            // Normal movement state
            if (this.golemModel.userData.lastAIPosition) {
                // Only update X/Z position, preserve Y for physics
                const newY = this.golemModel.position.y;
                this.golemModel.position.x = this.golemModel.userData.lastAIPosition.x;
                this.golemModel.position.z = this.golemModel.userData.lastAIPosition.z;
                this.golemModel.position.y = newY;

                // Update rotation only during movement states
                if (state === AIStates.MOVING || state === AIStates.STRAFING || state === AIStates.FLEEING) {
                    if (this.golemModel.userData.lastMoveDirection) {
                        const moveDir = this.golemModel.userData.lastMoveDirection;
                        if (moveDir.lengthSq() > 0.001) {
                            const flatDir = new THREE.Vector3(moveDir.x, 0, moveDir.z).normalize();
                            const targetPos = this.golemModel.position.clone().add(flatDir);
                            this.golemModel.lookAt(targetPos);
                        }
                    }
                }
            } else {
                // If no AI position, preserve current transform
                this.golemModel.position.copy(this.golemModel.userData.frozenState.position);
                this.golemModel.quaternion.copy(this.golemModel.userData.frozenState.quaternion);
                this.golemModel.rotation.copy(this.golemModel.userData.frozenState.rotation);
                this.golemModel.scale.copy(this.golemModel.userData.frozenState.scale);
            }
        }
    }

    /**
     * Apply idle animation
     * @param {number} time - Global time
     */
    applyIdleAnimation(time) {
        // Only reset child transforms to avoid fighting with AI/physics
        this.safeResetChildTransforms();

        const idleSpeed = this.animationData.idleSpeed;
        const idleSwayAmplitude = Math.PI / 40; // Very subtle movement for a heavy golem

        // Subtle idle movement - magma golems should move slowly and heavily
        if (this.bodyGroup) {
            // Slow, subtle body sway
            this.bodyGroup.rotation.z = Math.sin(time * idleSpeed * 0.3) * idleSwayAmplitude;
            // Slight forward lean
            this.bodyGroup.rotation.x = 0.05 + Math.sin(time * idleSpeed * 0.2) * (idleSwayAmplitude * 0.5);
        }

        // Head movement - very subtle for a golem
        if (this.headGroup) {
            this.headGroup.rotation.x = Math.sin(time * idleSpeed * 0.4) * (idleSwayAmplitude * 0.7);
            this.headGroup.rotation.z = Math.sin(time * idleSpeed * 0.3) * (idleSwayAmplitude * 0.5);
        }

        // Arms - heavy, hanging with minimal movement
        if (this.leftArmGroup) {
            this.leftArmGroup.rotation.x = Math.sin(time * idleSpeed * 0.25) * idleSwayAmplitude;
            this.leftArmGroup.rotation.z = 0.1; // Slightly out from body
        }
        if (this.rightArmGroup) {
            this.rightArmGroup.rotation.x = Math.sin(time * idleSpeed * 0.25 + Math.PI) * idleSwayAmplitude;
            this.rightArmGroup.rotation.z = -0.1; // Slightly out from body
        }

        // Legs - stable with minimal movement
        if (this.leftLegGroup && this.rightLegGroup) {
            // Very subtle weight shifting
            const weightShift = Math.sin(time * idleSpeed * 0.2) * (idleSwayAmplitude * 0.5);
            this.leftLegGroup.rotation.x = weightShift;
            this.rightLegGroup.rotation.x = -weightShift;
        }
    }

    /**
     * Apply walking animation
     * @param {number} time - Global time
     */
    applyWalkingAnimation(time) {
        // Only reset child transforms to avoid fighting with AI/physics
        this.safeResetChildTransforms();

        const walkSpeed = this.animationData.walkSpeed;
        const walkAmplitude = this.animationData.walkAmplitude;

        // Walk cycle for legs - slow, heavy steps
        if (this.leftLegGroup) {
            this.leftLegGroup.rotation.x = Math.sin(time * walkSpeed) * walkAmplitude;
        }
        if (this.rightLegGroup) {
            this.rightLegGroup.rotation.x = Math.sin(time * walkSpeed + Math.PI) * walkAmplitude;
        }

        // Arm swing - minimal for a heavy golem
        if (this.leftArmGroup) {
            this.leftArmGroup.rotation.x = Math.sin(time * walkSpeed + Math.PI) * (walkAmplitude * 0.4);
            this.leftArmGroup.rotation.z = 0.15; // Slightly out from body
        }
        if (this.rightArmGroup) {
            this.rightArmGroup.rotation.x = Math.sin(time * walkSpeed) * (walkAmplitude * 0.4);
            this.rightArmGroup.rotation.z = -0.15; // Slightly out from body
        }

        // Body movement - heavy, minimal sway
        if (this.bodyGroup) {
            // Side-to-side sway - very subtle
            this.bodyGroup.rotation.z = Math.sin(time * walkSpeed * 0.5) * (walkAmplitude * 0.1);
            // Forward lean - constant
            this.bodyGroup.rotation.x = 0.1;
        }

        // Head movement - follows body with minimal additional movement
        if (this.headGroup) {
            // Head follows body with slight delay
            this.headGroup.rotation.z = Math.sin(time * walkSpeed * 0.5 - 0.2) * (walkAmplitude * 0.1);
            // Slight up/down movement
            this.headGroup.rotation.x = Math.sin(time * walkSpeed) * 0.05;
        }
    }

    /**
     * Apply attacking animation
     * @param {number} time - Global time
     */
    applyAttackingAnimation(time) {
        // Only reset child transforms to avoid fighting with AI/physics
        this.safeResetChildTransforms();

        // Use attack progress (0 to 1) to determine animation phase
        const progress = this.attackProgress;

        // Divide into phases: windup (0-0.4), strike (0.4-0.7), recover (0.7-1.0)
        const windupPhase = progress < 0.4;
        const strikePhase = progress >= 0.4 && progress < 0.7;
        const recoverPhase = progress >= 0.7;

        // Determine which arm is attacking (alternate based on time)
        const isLeftArmAttack = Math.floor(time) % 2 === 0;
        const attackingArm = isLeftArmAttack ? this.leftArmGroup : this.rightArmGroup;
        const supportingArm = isLeftArmAttack ? this.rightArmGroup : this.leftArmGroup;

        // Body movement - heavy and powerful
        if (this.bodyGroup) {
            if (windupPhase) {
                // Wind up: lean back and rotate slightly
                const windupProgress = progress / 0.4; // 0 to 1 during windup phase
                this.bodyGroup.rotation.x = -0.2 * windupProgress;
                this.bodyGroup.rotation.y = (isLeftArmAttack ? -0.1 : 0.1) * windupProgress;
            } else if (strikePhase) {
                // Strike: lunge forward with rotation
                const strikeProgress = (progress - 0.4) / 0.3; // 0 to 1 during strike phase
                this.bodyGroup.rotation.x = -0.2 + (0.4 * strikeProgress);
                this.bodyGroup.rotation.y = (isLeftArmAttack ? -0.1 : 0.1) * (1 - strikeProgress);
            } else if (recoverPhase) {
                // Recover: return to neutral slowly
                const recoverProgress = (progress - 0.7) / 0.3; // 0 to 1 during recover phase
                this.bodyGroup.rotation.x = 0.2 - (0.2 * recoverProgress);
                this.bodyGroup.rotation.y = 0;
            }
        }

        // Arms - powerful striking motion
        if (attackingArm && supportingArm) {
            if (windupPhase) {
                // Wind up: raise attacking arm back
                const windupProgress = progress / 0.4;
                attackingArm.rotation.x = -Math.PI/3 * windupProgress;
                attackingArm.rotation.z = (isLeftArmAttack ? 0.3 : -0.3) * windupProgress;

                // Supporting arm moves forward slightly
                supportingArm.rotation.x = Math.PI/8 * windupProgress;
                supportingArm.rotation.z = (isLeftArmAttack ? -0.2 : 0.2) * windupProgress;
            } else if (strikePhase) {
                // Strike: powerful forward swing
                const strikeProgress = (progress - 0.4) / 0.3;
                attackingArm.rotation.x = -Math.PI/3 + (Math.PI/2 * strikeProgress);
                attackingArm.rotation.z = (isLeftArmAttack ? 0.3 : -0.3) * (1 - strikeProgress * 0.7);

                // Supporting arm provides balance
                supportingArm.rotation.x = Math.PI/8 + (Math.PI/12 * strikeProgress);
                supportingArm.rotation.z = (isLeftArmAttack ? -0.2 : 0.2) * (1 - strikeProgress * 0.5);
            } else if (recoverPhase) {
                // Recover: return to neutral slowly
                const recoverProgress = (progress - 0.7) / 0.3;
                attackingArm.rotation.x = Math.PI/6 - (Math.PI/6 * recoverProgress);
                attackingArm.rotation.z = (isLeftArmAttack ? 0.1 : -0.1) * (1 - recoverProgress);

                supportingArm.rotation.x = Math.PI/6 - (Math.PI/6 * recoverProgress);
                supportingArm.rotation.z = (isLeftArmAttack ? -0.1 : 0.1) * (1 - recoverProgress);
            }
        }

        // Head follows the attack motion
        if (this.headGroup) {
            if (windupPhase) {
                // Look in direction of attack during windup
                this.headGroup.rotation.x = -0.1;
                this.headGroup.rotation.y = (isLeftArmAttack ? -0.2 : 0.2) * (progress / 0.4);
            } else if (strikePhase) {
                // Look forward during strike
                const strikeProgress = (progress - 0.4) / 0.3;
                this.headGroup.rotation.x = -0.1 + (0.2 * strikeProgress);
                this.headGroup.rotation.y = (isLeftArmAttack ? -0.2 : 0.2) * (1 - strikeProgress);
            } else if (recoverPhase) {
                // Return to neutral
                const recoverProgress = (progress - 0.7) / 0.3;
                this.headGroup.rotation.x = 0.1 - (0.1 * recoverProgress);
                this.headGroup.rotation.y = 0;
            }
        }

        // Legs provide stable base
        if (this.leftLegGroup && this.rightLegGroup) {
            // Wider stance during attack
            this.leftLegGroup.rotation.z = -0.15;
            this.rightLegGroup.rotation.z = 0.15;

            // Bend knees during strike for power
            if (strikePhase) {
                const strikeProgress = (progress - 0.4) / 0.3;
                this.leftLegGroup.rotation.x = 0.15 * strikeProgress;
                this.rightLegGroup.rotation.x = 0.15 * strikeProgress;
            } else if (recoverPhase) {
                const recoverProgress = (progress - 0.7) / 0.3;
                this.leftLegGroup.rotation.x = 0.15 * (1 - recoverProgress);
                this.rightLegGroup.rotation.x = 0.15 * (1 - recoverProgress);
            }
        }
    }

    /**
     * Apply hit reaction animation
     * @param {number} time - Global time
     */
    applyHitReactionAnimation(time) {
        // Only reset child transforms during hit reaction to avoid fighting with physics
        this.safeResetChildTransforms();

        // Calculate time since hit
        const timeSinceHit = time - this.lastHitTime;
        const hitReactionDuration = 0.6; // 0.6 seconds
        const progress = Math.min(1.0, timeSinceHit / hitReactionDuration);

        // Magma golems should have minimal hit reactions - they're made of rock!

        // Body reaction - slight stagger
        if (this.bodyGroup) {
            // Slight backward lean
            this.bodyGroup.rotation.x = -0.1 * (1 - progress);
            // Minimal wobble
            this.bodyGroup.rotation.z = Math.sin(time * 10) * 0.05 * (1 - progress);
        }

        // Arms reaction - minimal movement
        if (this.leftArmGroup) {
            this.leftArmGroup.rotation.x = 0.1 * (1 - progress);
            this.leftArmGroup.rotation.z = 0.2 + Math.sin(time * 8) * 0.1 * (1 - progress);
        }

        if (this.rightArmGroup) {
            this.rightArmGroup.rotation.x = 0.1 * (1 - progress);
            this.rightArmGroup.rotation.z = -0.2 - Math.sin(time * 8) * 0.1 * (1 - progress);
        }

        // Head reaction - slight movement
        if (this.headGroup) {
            this.headGroup.rotation.x = -0.15 * (1 - progress);
            this.headGroup.rotation.z = Math.sin(time * 12) * 0.05 * (1 - progress);
        }

        // Legs - minimal reaction
        if (this.leftLegGroup && this.rightLegGroup) {
            // Slight bend in knees
            const kneeBend = 0.05 * (1 - progress);
            this.leftLegGroup.rotation.x = kneeBend;
            this.rightLegGroup.rotation.x = kneeBend;
        }
    }
}
