/**
 * Zombie Animation Handler
 * Delegates to SimpleMagmaGolemAnimationHandler for logic.
 */

import { SimpleMagmaGolemAnimationHandler } from './SimpleMagmaGolemAnimationHandler.js';

export class ZombieAnimationHandler extends SimpleMagmaGolemAnimationHandler {
    /**
     * Create a zombie animation handler
     * @param {THREE.Group} zombieModel - The zombie model to animate
     */
    constructor(zombieModel) {
        super(zombieModel);
    }
}
