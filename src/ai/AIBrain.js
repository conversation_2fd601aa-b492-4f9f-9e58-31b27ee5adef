/**
 * Base AI Brain class that all AI implementations will inherit from
 * Provides common functionality and interface for all AI types
 */
import * as THREE from 'three';
import { AIStates } from './AIStates.js';

export class AIBrain {
    /**
     * Constructor for the base AI Brain
     * @param {Object} enemy - The enemy object this brain controls
     * @param {Object} enemyData - The enemy data (from userData)
     * @param {Object} scene - The scene object
     * @param {Object} player - The player object
     * @param {Number} difficulty - Difficulty level (1-5)
     */
    constructor(enemy, enemyData, scene, player, difficulty = 1) {
        this.enemy = enemy;
        this.enemyData = enemyData;
        this.scene = scene;
        this.player = player;

        // Set difficulty (1-5)
        this.difficulty = Math.max(1, Math.min(5, difficulty || 1));

        // State management
        this.currentState = AIStates.IDLE;
        this.stateTimer = 0;
        this.previousState = null;

        // Memory system
        this.memory = {
            playerAttackPatterns: [],
            playerDefenseStyle: null, // 'block', 'dodge', or null
            playerPreferredRange: null,
            lastEncounterTime: 0,
            encounterCount: 0
        };

        // Movement
        this.moveDirection = new THREE.Vector3();
        this.targetPosition = new THREE.Vector3();
        this.velocity = new THREE.Vector3();

        // Combat
        this.timeSinceLastAttack = 0;
        this.attackCooldown = this._getScaledValue(enemyData.attackCooldown || 2.0);
        this.isAiming = false;
        this.aimDuration = this._getScaledValue(0.5, 0.3, 0.7); // Scale based on difficulty

        // Hit reaction
        this.isHitReacting = false;
        this.hitReactionTimer = 0;
        this.hitReactionDuration = 0.3; // Duration of hit reaction in seconds

        // Dodging
        this.isDodging = false;
        this.dodgeTimer = 0;
        this.dodgeCooldown = 0;
        this.dodgeCooldownTimer = 0;
        this.dodgeTriggerDistance = this._getScaledValue(5, 3, 7);
        this.dodgeDuration = this._getScaledValue(0.5, 0.3, 0.7);
        this.dodgeChance = this._getScaledValue(0.2, 0.1, 0.5); // Scale based on difficulty

        // Idle behavior
        this.idleTimer = 0;
        this.idleDuration = this._getScaledValue(2, 1, 3);
        this.shouldWander = Math.random() < this._getScaledValue(0.3, 0.1, 0.5);

        // Hit reaction parameters
        this.isHitReacting = false;
        this.hitReactionTimer = 0;
        this.hitReactionDuration = 0.6; // Adjusted back to original duration

        // Knockback physics parameters
        this.isKnockedBack = false;
        this.knockbackTimer = 0;
        this.knockbackDuration = 0.6; // Adjusted back to original duration for faster recovery
        this.knockbackDirection = new THREE.Vector3();
        this.initialKnockbackPosition = null;

        // Physics-based motion parameters
        this.knockbackInitialSpeed = 0;
        this.knockbackCurrentSpeed = 0;
        this.knockbackDeceleration = 0;
        this.knockbackVerticalVelocity = 0;
        this.knockbackGravity = 0;

        // Store the original up vector to ensure proper orientation
        this.originalUpVector = new THREE.Vector3(0, 1, 0);

        // Flying specific (if applicable)
        this.isFlying = false;
        this.hoverHeight = 0;
        this.maxHoverHeight = 0;
        this.minHoverHeight = 0;

        // Collision detection
        this.collisionObjects = null;
        this.floorBounds = null;

        // Initialize based on difficulty
        this._initializeWithDifficulty();
    }

    /**
     * Initialize AI parameters based on difficulty level
     * @private
     */
    _initializeWithDifficulty() {
        // Scale parameters based on difficulty
        this.reactionTime = this._getScaledValue(0.5, 0.8, 0.2); // Lower is better
        this.accuracy = this._getScaledValue(0.7, 0.5, 0.9); // Higher is better
        this.aggressiveness = this._getScaledValue(0.5, 0.3, 0.8); // Higher is more aggressive

        // Add random variation to make enemies feel different
        const variation = 0.1; // 10% variation
        this.reactionTime *= (1 - variation/2 + Math.random() * variation);
        this.accuracy *= (1 - variation/2 + Math.random() * variation);
        this.aggressiveness *= (1 - variation/2 + Math.random() * variation);
    }

    /**
     * Scale a value based on difficulty level
     * @param {Number} baseValue - The base value at difficulty 3
     * @param {Number} minValue - The minimum value at difficulty 1 (optional)
     * @param {Number} maxValue - The maximum value at difficulty 5 (optional)
     * @returns {Number} - The scaled value
     * @private
     */
    _getScaledValue(baseValue, minValue = null, maxValue = null) {
        // If min/max not provided, scale by percentage
        if (minValue === null) minValue = baseValue * 0.7;
        if (maxValue === null) maxValue = baseValue * 1.3;

        // Linear interpolation based on difficulty
        const normalizedDifficulty = (this.difficulty - 1) / 4; // 0 to 1
        return minValue + normalizedDifficulty * (maxValue - minValue);
    }

    /**
     * Update the AI brain
     * @param {Number} deltaTime - Time since last update
     * @param {Array} collisionObjects - Objects to check for collision
     * @param {Object} floorBounds - Bounds of the floor
     * @returns {Object} - Updated state data
     */
    update(deltaTime, collisionObjects, floorBounds) {
        // CRITICAL FIX: Only ensure enemy is upright if not in knockback
        if (!this.isKnockedBack) {
            this._ensureEnemyIsUpright();
        }

        // Store collision objects and floor bounds for use in movement methods
        this.collisionObjects = collisionObjects;
        this.floorBounds = floorBounds;

        // Update timers
        this.stateTimer += deltaTime;
        this.timeSinceLastAttack += deltaTime;
        if (this.dodgeCooldownTimer > 0) this.dodgeCooldownTimer -= deltaTime;
        // NOTE: knockbackTimer is handled in the knockback section below

        // Handle hit reaction
        if (this.isHitReacting) {
            this.hitReactionTimer += deltaTime;

            // CRITICAL FIX: Ensure enemy stays upright during hit reaction
            this._ensureEnemyIsUpright();

            // End hit reaction after duration
            if (this.hitReactionTimer >= this.hitReactionDuration) {
                this.isHitReacting = false;
                this.hitReactionTimer = 0;

                // EMERGENCY FIX: Unfreeze rotation and restore pre-hit rotation
                if (this.enemy) {
                    // Unfreeze rotation
                    this.enemy.userData.freezeRotation = false;

                    // Restore pre-hit rotation if available
                    if (this.enemy.userData.preHitRotation) {
                        this.enemy.quaternion.copy(this.enemy.userData.preHitRotation);
                    }
                }

                // IMPORTANT: Transition back to IDLE state when hit reaction is complete
                if (this.currentState === AIStates.HIT_REACTING) {
                    this.setState(AIStates.IDLE);
                    console.log(`[EMERGENCY FIX] ${this.enemy ? this.enemy.name : 'Unknown enemy'} recovered from hit reaction, rotation unfrozen`);
                }
            }

            // Skip normal AI processing during hit reaction
            if (this.isHitReacting) {
                return {
                    state: AIStates.HIT_REACTING, // Force HIT_REACTING state during hit reaction
                    stateTimer: this.stateTimer,
                    isAiming: this.isAiming,
                    isDodging: this.isDodging,
                    isHitReacting: true
                };
            }
        }

        // Reset movement if knocked back
        if (this.isKnockedBack) {
            // CRITICAL FIX: Always decrement knockback timer
            this.knockbackTimer -= deltaTime;

            // CRITICAL FIX: Check if we need to correct rotation during knockback
            if (this.enemy.userData.needsUprightCorrection) {
                const now = performance.now();
                if (now - this.enemy.userData.lastCorrectionTime >= this.enemy.userData.correctionInterval) {
                    this._ensureEnemyIsUpright();
                    this.enemy.userData.lastCorrectionTime = now;
                }
            }

            if (this.knockbackTimer <= 0) {
                this.isKnockedBack = false;
                this.initialKnockbackY = undefined;
                this.enemy.userData.needsUprightCorrection = false;

                // Allow rotation again now that knockback is over
                if (this.enemy) {
                    this.enemy.userData.freezeRotation = false;
                }

                this._ensureEnemyIsUpright(); // Final orientation correction

                // CRITICAL FIX: Transition to IDLE state when knockback ends
                this.setState(AIStates.IDLE);

                console.log(`[AIBrain] Knockback completed for ${this.enemy.name}, transitioning to IDLE`);
            } else {
                // CRITICAL FIX: Always apply knockback physics while timer is active
                this._applyKnockback(deltaTime);
            }

            // CRITICAL FIX: Always return knockback state during knockback
            return { state: AIStates.KNOCKBACK, timer: this.knockbackTimer };
        }

        // Calculate distance to player
        const directionToPlayer = this._getDirectionToPlayer();
        const distanceToPlayer = this._getDistanceToPlayer();

        // Update state based on current state and conditions
        this._updateState(deltaTime, distanceToPlayer, directionToPlayer);

        // Execute behavior based on current state
        this._executeBehavior(deltaTime, distanceToPlayer, directionToPlayer, collisionObjects, floorBounds);

        // Return updated state data
        return { state: this.currentState, timer: this.stateTimer };
    }

    /**
     * Update the AI state based on current conditions
     * @param {Number} deltaTime - Time since last update
     * @param {Number} distanceToPlayer - Distance to player
     * @param {THREE.Vector3} directionToPlayer - Direction to player
     * @private
     */
    _updateState(deltaTime, distanceToPlayer, directionToPlayer) {
        // This is a base implementation that should be overridden by subclasses
        // Default behavior: transition to IDLE if no other state is active
        if (this.currentState === null) {
            this.setState(AIStates.IDLE);
        }
    }

    /**
     * Execute behavior based on current state
     * @param {Number} deltaTime - Time since last update
     * @param {Number} distanceToPlayer - Distance to player
     * @param {THREE.Vector3} directionToPlayer - Direction to player
     * @param {Array} collisionObjects - Objects to check for collision
     * @param {Object} floorBounds - Bounds of the floor
     * @private
     */
    _executeBehavior(deltaTime, distanceToPlayer, directionToPlayer, collisionObjects, floorBounds) {
        // This is a base implementation that should be overridden by subclasses
        // Default behavior: do nothing
    }

    /**
     * Set the current state
     * @param {String} newState - The new state
     */
    setState(newState) {
        // CRITICAL FIX: Prevent state changes during knockback unless it's ending
        if (this.isKnockedBack && newState !== AIStates.KNOCKBACK) {
            console.log(`DEBUG: Blocked state transition from ${this.currentState} to ${newState} during knockback`);
            return; // Block the state change
        }

        if (this.currentState !== newState) {
            console.log(`DEBUG: State transition from ${this.currentState} to ${newState}`);
            this.previousState = this.currentState;
            this.currentState = newState;
            this.stateTimer = 0;

            try {
                this.onStateEnter(newState);
                console.log(`DEBUG: onStateEnter completed for ${newState}`);
            } catch (error) {
                console.error(`ERROR in onStateEnter for ${newState}:`, error);
            }
        } else {
            // Debug when trying to set the same state
            console.log(`DEBUG: Attempted to set same state ${newState} (no change)`);
        }
    }

    /**
     * Called when entering a new state
     * @param {String} state - The state being entered
     */
    onStateEnter(state) {
        // This is a base implementation that should be overridden by subclasses
        // Default behavior: do nothing
    }

    /**
     * Trigger a hit reaction when the enemy is hit
     */
    triggerHitReaction() {
        // EMERGENCY FIX: Store the current rotation quaternion
        // This will be used to restore rotation after hit reaction
        if (this.enemy) {
            if (!this.enemy.userData.preHitRotation) {
                this.enemy.userData.preHitRotation = new THREE.Quaternion();
            }
            // Store current rotation
            this.enemy.userData.preHitRotation.copy(this.enemy.quaternion);

            // CRITICAL: Store the current transform state
            if (!this.enemy.userData.frozenTransform) {
                this.enemy.userData.frozenTransform = {
                    position: this.enemy.position.clone(),
                    quaternion: this.enemy.quaternion.clone(),
                    scale: this.enemy.scale.clone()
                };
            }
        }

        // Reset hit reaction state
        this.isHitReacting = true;
        this.hitReactionTimer = 0;

        // CRITICAL: Completely freeze rotation during hit reaction
        // This is the most reliable way to prevent spinning glitches
        if (this.enemy) {
            // Flag to prevent rotation changes during hit reaction
            this.enemy.userData.freezeRotation = true;

            // Store the exact moment the rotation was frozen
            this.enemy.userData.rotationFrozenAt = Date.now();

            // Store the current world matrix to ensure transform consistency
            if (!this.enemy.userData.frozenWorldMatrix) {
                this.enemy.userData.frozenWorldMatrix = this.enemy.matrixWorld.clone();
            }
        }

        // Set state to HIT_REACTING
        this.setState(AIStates.HIT_REACTING);

        // Reset the animation timer in the enemy's userData
        if (this.enemy && this.enemy.userData) {
            this.enemy.userData.lastHitTime = Date.now() / 1000; // Current time in seconds
        }

        // Log hit reaction for debugging
        console.log(`[EMERGENCY FIX] Triggering hit reaction with frozen rotation for ${this.enemy ? this.enemy.name : 'unknown enemy'}`);
    }

    /**
     * Apply knockback to the enemy with realistic physics
     * @param {THREE.Vector3} direction - Direction of knockback
     * @param {Number} strength - Strength of knockback
     */
    applyKnockback(direction, strength) {
        // Initialize knockback state
        this.isKnockedBack = true;
        this.knockbackTimer = this.knockbackDuration;

        // CRITICAL FIX: Set state to KNOCKBACK to trigger knockback animations
        this.setState(AIStates.KNOCKBACK);

        // Prevent any further rotation while the enemy is airborne
        if (this.enemy) {
            // Store current transform state if not already stored
            if (!this.enemy.userData.frozenTransform) {
                this.enemy.userData.frozenTransform = {
                    position: this.enemy.position.clone(),
                    quaternion: this.enemy.quaternion.clone(),
                    scale: this.enemy.scale.clone()
                };
            }

            // CRITICAL: Store the world matrix at the moment of freezing
            if (!this.enemy.userData.frozenWorldMatrix) {
                this.enemy.userData.frozenWorldMatrix = this.enemy.matrixWorld.clone();
            }

            this.enemy.userData.freezeRotation = true;
            this.enemy.userData.rotationFrozenAt = Date.now();
        }

        // Store the initial position when knockback starts
        this.initialKnockbackPosition = this.enemy.position.clone();

        // Normalize the knockback direction (purely horizontal)
        const normalizedDirection = direction.clone();
        normalizedDirection.y = 0; // Keep it horizontal for realistic physics
        normalizedDirection.normalize();

        // Store the knockback direction
        this.knockbackDirection = normalizedDirection.clone();

        // Calculate knockback physics parameters
        const enemyMass = this.enemyData.mass || 1.0;

        // Initial velocity (units per second)
        const initialSpeed = (strength / enemyMass) * 4.0;

        // Store physics parameters
        this.knockbackInitialSpeed = initialSpeed;
        this.knockbackCurrentSpeed = initialSpeed;
        this.knockbackDeceleration = initialSpeed / (this.knockbackDuration * 1.2);
        this.knockbackVerticalVelocity = initialSpeed * 0.25;
        this.knockbackGravity = this.knockbackVerticalVelocity * 1.6 / this.knockbackDuration;

        // CRITICAL FIX: Add continuous rotation correction during knockback
        this.enemy.userData.needsUprightCorrection = true;
        this.enemy.userData.lastCorrectionTime = 0;
        this.enemy.userData.correctionInterval = 1/60; // Check every frame
    }

    /**
     * Apply knockback movement using realistic physics
     * @param {Number} deltaTime - Time since last update
     * @private
     */
    _applyKnockback(deltaTime) {
        // Update physics parameters for smooth, continuous flight
        const knockbackProgress = 1 - (this.knockbackTimer / this.knockbackDuration);

        // IMPROVED FLIGHT: Use a cubic ease-out curve for even smoother deceleration
        // This creates a very smooth flight that maintains momentum longer
        const t = knockbackProgress;
        const easingFactor = 1 - (t * t * t); // Cubic ease-out for smoother flight

        // Calculate current horizontal speed with easing
        // This maintains more speed through the middle of the flight
        this.knockbackCurrentSpeed = this.knockbackInitialSpeed * easingFactor;

        // IMPROVED ARC: Use a better curve for vertical movement
        // This creates a more natural arc that peaks in the middle
        const arcFactor = 4 * t * (1 - t); // Quadratic bezier curve peaking at t=0.5
        const verticalFactor = arcFactor - (0.5 * t); // Adjust to create proper arc

        // Set vertical velocity based on arc position - adjusted for appropriate height
        this.knockbackVerticalVelocity = this.knockbackInitialSpeed * 0.25 * verticalFactor; // Reduced from 0.3 to 0.25 for more controlled height

        // Calculate horizontal movement with current speed
        const horizontalMove = this.knockbackDirection.clone()
            .multiplyScalar(this.knockbackCurrentSpeed * deltaTime);

        // Calculate vertical movement
        const verticalMove = this.knockbackVerticalVelocity * deltaTime;

        // Calculate new position
        const newPosition = this.enemy.position.clone().add(horizontalMove);
        newPosition.y += verticalMove;

        // Ensure enemy doesn't go below the floor
        const floorY = this.initialKnockbackPosition.y;
        if (newPosition.y < floorY) {
            newPosition.y = floorY;
            this.knockbackVerticalVelocity = 0; // Stop vertical movement when hitting the floor
        }

        // Check floor bounds
        if (this.floorBounds) {
            // Clamp position to floor bounds
            const minX = this.floorBounds.min.x + 1.0; // Add buffer
            const maxX = this.floorBounds.max.x - 1.0;
            const minZ = this.floorBounds.min.z + 1.0;
            const maxZ = this.floorBounds.max.z - 1.0;

            newPosition.x = Math.max(minX, Math.min(maxX, newPosition.x));
            newPosition.z = Math.max(minZ, Math.min(maxZ, newPosition.z));
        }

        // SIMPLIFIED: Skip collision detection during knockback to ensure movement
        // Knockback should override collision detection for dramatic effect
        let canMove = true;

        // Apply movement - force position update
        this.enemy.position.copy(newPosition);

        // Force matrix update to ensure the change takes effect
        this.enemy.updateMatrixWorld(true);

        // If knockback is nearly complete, ensure smooth landing
        if (this.knockbackTimer < 0.1) {
            // Gradually blend back to floor level in the last 0.1 seconds
            const returnFactor = 1 - (this.knockbackTimer / 0.1); // 0->1 over last 0.1 seconds
            const targetY = this.initialKnockbackPosition.y;
            const currentY = this.enemy.position.y;

            // Smoothly interpolate to floor level
            this.enemy.position.y = currentY * (1 - returnFactor) + targetY * returnFactor;
        }

        // Ensure the enemy stays upright by correcting its rotation
        // For skeletons, we need to be extra careful about rotation
        const isSkeletonModel = this.enemy.name.includes('skeleton') ||
                                   this.enemy.name.includes('Skeleton') ||
                                   this.enemyData?.type?.includes('skeleton');

        if (isSkeletonModel) {
            // For skeletons, apply a more thorough rotation correction
            this._ensureEnemyIsUpright();

            // Double-check that the enemy is not upside down
            const up = new THREE.Vector3(0, 1, 0).applyQuaternion(this.enemy.quaternion);
            if (up.y < 0.9) { // If the up vector is not pointing mostly up
                this._ensureEnemyIsUpright(); // Apply correction again
            }
        } else {
            // For other enemies, standard correction is sufficient
            this._ensureEnemyIsUpright();
        }
    }

    /**
     * Ensure the enemy stays upright by correcting its rotation
     * @private
     */
    _ensureEnemyIsUpright() {
        // Guard against missing enemy
        if (!this.enemy) return;

        // CRITICAL: Do not change orientation when rotation is frozen
        if (this.enemy.userData?.freezeRotation) {
            // If we have a frozen transform, ensure it's maintained
            if (this.enemy.userData.frozenTransform) {
                this.enemy.position.copy(this.enemy.userData.frozenTransform.position);
                this.enemy.quaternion.copy(this.enemy.userData.frozenTransform.quaternion);
                this.enemy.scale.copy(this.enemy.userData.frozenTransform.scale);

                // Ensure world matrix is consistent
                if (this.enemy.userData.frozenWorldMatrix) {
                    this.enemy.matrixWorld.copy(this.enemy.userData.frozenWorldMatrix);
                }
            }
            return;
        }

        const rot = this.enemy.rotation;
        if (!rot) return;

        // Only correct if noticeably tilted
        const tiltThreshold = 0.2; // radians (~11°)
        if (Math.abs(rot.x) < tiltThreshold && Math.abs(rot.z) < tiltThreshold) {
            return; // Orientation is fine
        }

        // Zero out pitch/roll while keeping current yaw
        rot.x = 0;
        rot.z = 0;

        // Update matrices so child transforms remain consistent
        this.enemy.updateMatrixWorld(true);
    }

    /**
     * Get direction to player
     * @returns {THREE.Vector3} - Direction to player
     * @private
     */
    _getDirectionToPlayer() {
        if (!this.player) return new THREE.Vector3();

        const direction = this.player.position.clone().sub(this.enemy.position);
        direction.y = 0; // Ignore vertical difference for most AI types
        return direction.normalize();
    }

    /**
     * Get distance to player
     * @returns {Number} - Distance to player
     * @private
     */
    _getDistanceToPlayer() {
        if (!this.player) return Infinity;

        const direction = this.player.position.clone().sub(this.enemy.position);
        direction.y = 0; // Ignore vertical difference for most AI types
        return direction.length();
    }

    /**
     * Face the enemy towards a target
     * @param {THREE.Vector3} targetPosition - Position to face
     * @private
     */
    _faceTarget(targetPosition) {
        // CRITICAL: Only update rotation if not frozen
        if (this.enemy?.userData?.freezeRotation) {
            // If frozen, ensure the frozen transform is maintained
            if (this.enemy.userData.frozenTransform) {
                this.enemy.position.copy(this.enemy.userData.frozenTransform.position);
                this.enemy.quaternion.copy(this.enemy.userData.frozenTransform.quaternion);
                this.enemy.scale.copy(this.enemy.userData.frozenTransform.scale);

                // Ensure world matrix is consistent
                if (this.enemy.userData.frozenWorldMatrix) {
                    this.enemy.matrixWorld.copy(this.enemy.userData.frozenWorldMatrix);
                }
            }
            return;
        }

        const direction = targetPosition.clone().sub(this.enemy.position);
        if (direction.lengthSq() < 0.001) return;

        direction.y = 0; // Keep upright
        this.enemy.lookAt(this.enemy.position.clone().add(direction));
    }

    /**
     * Face a target position with organic, human-like movement
     * @param {THREE.Vector3} targetPosition - Position to face
     * @param {Number} deltaTime - Time since last update
     * @private
     */
    _faceTargetOrganic(targetPosition, deltaTime) {
        if (!this.enemy) return;

        // Initialize rotation properties if not set
        if (!this.currentRotationSpeed) {
            this.currentRotationSpeed = 0;
        }
        if (!this.targetYRotation) {
            this.targetYRotation = this.enemy.rotation.y;
        }

        // Calculate direction to target
        const direction = targetPosition.clone().sub(this.enemy.position).normalize();

        if (direction.lengthSq() > 0.001) {
            // Create a temporary object to get the target rotation
            const tempObj = new THREE.Object3D();
            tempObj.position.copy(this.enemy.position);
            tempObj.lookAt(targetPosition);

            // Get the target Y rotation
            this.targetYRotation = tempObj.rotation.y;

            // Calculate the difference between current and target rotation
            let rotationDiff = this.targetYRotation - this.enemy.rotation.y;

            // Normalize the rotation difference to be between -PI and PI
            while (rotationDiff > Math.PI) rotationDiff -= Math.PI * 2;
            while (rotationDiff < -Math.PI) rotationDiff += Math.PI * 2;

            // Calculate rotation speed based on the difference
            const maxRotationSpeed = 5.0; // Maximum rotation speed in radians per second
            const targetRotationSpeed = Math.sign(rotationDiff) * Math.min(Math.abs(rotationDiff) * 3, maxRotationSpeed);

            // Smoothly adjust current rotation speed towards target speed
            const rotationAcceleration = 10.0; // How quickly to change rotation speed
            this.currentRotationSpeed += (targetRotationSpeed - this.currentRotationSpeed) * Math.min(rotationAcceleration * deltaTime, 1.0);

            // Add slight randomness to rotation speed (human imperfection)
            this.currentRotationSpeed += (Math.random() - 0.5) * 0.1;

            // Apply rotation
            this.enemy.rotation.y += this.currentRotationSpeed * deltaTime;

            // Add slight head tilt (looking up/down) if defined
            if (this.lookYOffset !== undefined) {
                // Gradually apply the head tilt
                if (!this.currentHeadTilt) this.currentHeadTilt = 0;
                this.currentHeadTilt += (this.lookYOffset - this.currentHeadTilt) * Math.min(2.0 * deltaTime, 1.0);
                this.enemy.rotation.x = this.currentHeadTilt;
            }
        }
    }

    /**
     * Check if the enemy can see the player
     * @returns {Boolean} - True if player is visible
     * @private
     */
    _canSeePlayer() {
        if (!this.player) return false;

        // Simple line of sight check
        const direction = this.player.position.clone().sub(this.enemy.position);
        const distance = direction.length();
        direction.normalize();

        // Get max range from enemy data if available, otherwise use a default
        const maxRange = this.enemyData.maxRange || 30.0;

        // Check if player is within max detection range
        if (distance > maxRange) {
            // Occasionally log when player is out of range
            if (Math.random() < 0.01) { // 1% chance to avoid log spam
                console.log(`[AIBrain] ${this.enemy.name} - Player out of detection range: ${distance.toFixed(1)} > ${maxRange.toFixed(1)}`);
            }
            return false;
        }

        // Raycasting to check for obstacles
        const raycaster = new THREE.Raycaster(
            this.enemy.position.clone(),
            direction,
            0,
            distance
        );

        // TODO: Implement actual raycasting against collision objects
        // For now, just return true if within range
        return true;
    }

    /**
     * Update the enemy's memory of the player
     * @param {Object} playerAction - The player's action
     * @private
     */
    _updateMemory(playerAction) {
        // Update memory based on player action
        if (playerAction.type === 'attack') {
            this.memory.playerAttackPatterns.push({
                type: playerAction.attackType,
                time: Date.now()
            });

            // Keep only the last 5 attacks
            if (this.memory.playerAttackPatterns.length > 5) {
                this.memory.playerAttackPatterns.shift();
            }
        } else if (playerAction.type === 'defense') {
            // Update defense style based on frequency
            if (!this.memory.playerDefenseStyle) {
                this.memory.playerDefenseStyle = playerAction.defenseType;
            } else {
                // Simple weighted average
                this.memory.playerDefenseStyle =
                    playerAction.defenseType === this.memory.playerDefenseStyle ?
                    playerAction.defenseType :
                    (Math.random() < 0.7 ? this.memory.playerDefenseStyle : playerAction.defenseType);
            }
        }

        // Update encounter info
        this.memory.lastEncounterTime = Date.now();
        this.memory.encounterCount++;
    }

    /**
     * Predict player movement based on input
     * @returns {THREE.Vector3} - Predicted player position
     * @private
     */
    _predictPlayerMovement() {
        if (!this.player) return null;

        // Simple prediction based on current velocity
        // In a real implementation, you would use player input and physics
        const predictedPosition = this.player.position.clone();

        // If player has velocity, predict movement
        if (this.player.velocity) {
            const predictionTime = this._getScaledValue(0.5, 0.2, 1.0); // Scale with difficulty
            predictedPosition.add(this.player.velocity.clone().multiplyScalar(predictionTime));
        }

        return predictedPosition;
    }

    /**
     * Check for nearby projectiles and decide whether to dodge
     * @param {Array} projectiles - Active projectiles
     * @returns {Boolean} - True if should dodge
     * @private
     */
    _checkForDodge(projectiles) {
        if (this.dodgeCooldownTimer > 0 || this.isKnockedBack) return false;

        // Check if any projectiles are close and coming towards the enemy
        for (const projectile of projectiles) {
            // Skip enemy projectiles
            if (projectile.userData && projectile.userData.isEnemyProjectile) continue;

            // Calculate distance and direction
            const distSq = this.enemy.position.distanceToSquared(projectile.position);

            // Check if projectile is close enough and dodge chance succeeds
            if (distSq < (this.dodgeTriggerDistance * this.dodgeTriggerDistance) &&
                Math.random() < this.dodgeChance) {
                return true;
            }
        }

        return false;
    }

    /**
     * Execute a dodge maneuver
     * @param {Array} projectiles - Active projectiles
     * @param {Number} deltaTime - Time since last update
     * @private
     */
    _executeDodge(projectiles, deltaTime) {
        if (!this.isDodging) {
            // Start dodge
            this.isDodging = true;
            this.dodgeTimer = this.dodgeDuration;

            // Determine dodge direction (perpendicular to closest projectile)
            let closestProjectile = null;
            let closestDistSq = Infinity;

            for (const projectile of projectiles) {
                if (projectile.userData && projectile.userData.isEnemyProjectile) continue;

                const distSq = this.enemy.position.distanceToSquared(projectile.position);
                if (distSq < closestDistSq) {
                    closestDistSq = distSq;
                    closestProjectile = projectile;
                }
            }

            if (closestProjectile) {
                // Calculate dodge direction (perpendicular to projectile velocity)
                const projectileDir = closestProjectile.userData.velocity.clone().normalize();
                this.dodgeDirection = new THREE.Vector3(-projectileDir.z, 0, projectileDir.x);

                // Randomly choose left or right perpendicular
                if (Math.random() < 0.5) {
                    this.dodgeDirection.multiplyScalar(-1);
                }
            } else {
                // Fallback: dodge in random direction
                const angle = Math.random() * Math.PI * 2;
                this.dodgeDirection = new THREE.Vector3(Math.cos(angle), 0, Math.sin(angle));
            }
        }

        // Continue dodge movement
        if (this.dodgeTimer > 0) {
            this.dodgeTimer -= deltaTime;

            // Move in dodge direction
            const dodgeSpeed = this.enemyData.speed * 2.0; // Faster than normal movement
            const dodgeMove = this.dodgeDirection.clone().multiplyScalar(dodgeSpeed * deltaTime);

            // Apply movement
            this.enemy.position.add(dodgeMove);

            // Face in movement direction
            this._faceTarget(this.enemy.position.clone().add(this.dodgeDirection));

            // End dodge if timer expired
            if (this.dodgeTimer <= 0) {
                this.isDodging = false;
                this.dodgeCooldownTimer = this._getScaledValue(2.0, 3.0, 1.0); // Longer cooldown for lower difficulty
            }

            return true; // Dodge executed
        }

        return false; // No dodge executed
    }

    /**
     * Execute idle behavior (wandering, looking around)
     * @param {Number} deltaTime - Time since last update
     * @private
     */
    _executeIdleBehavior(deltaTime) {
        this.idleTimer += deltaTime;

        // Initialize acceleration if not set
        if (!this.currentAcceleration) {
            this.currentAcceleration = 0;
        }

        // Add micro-movements even when standing still (breathing, shifting weight)
        // But ensure the enemy stays grounded
        const microMovement = Math.sin(this.idleTimer * 2) * 0.01;

        // Store the base Y position if not already set
        if (this.baseYPosition === undefined) {
            // Set base Y position to half the enemy's height (assuming center pivot)
            const enemyBox = new THREE.Box3().setFromObject(this.enemy);
            const enemyHeight = enemyBox.max.y - enemyBox.min.y;
            this.baseYPosition = enemyHeight / 2;
        }

        // Apply micro-movement but ensure Y position stays at base level
        // CRITICAL FIX: Don't override Y position during knockback
        if (!this.isKnockedBack) {
            this.enemy.position.y = this.baseYPosition + microMovement;
        }

        // Decide whether to wander or stand still
        if (this.shouldWander) {
            // Wander behavior
            if (this.idleTimer >= this.idleDuration) {
                // Choose new random direction and duration with more variation
                const angle = Math.random() * Math.PI * 2;
                this.moveDirection.set(Math.cos(angle), 0, Math.sin(angle));

                // Add slight randomness to direction
                this.moveDirection.x += (Math.random() - 0.5) * 0.3;
                this.moveDirection.z += (Math.random() - 0.5) * 0.3;
                this.moveDirection.normalize();

                // More varied durations
                this.idleDuration = this._getScaledValue(2, 0.8, 3.5);
                this.idleTimer = 0;

                // Reset acceleration for natural movement
                this.currentAcceleration = 0;
            }

            // Apply acceleration for more natural movement
            const maxAcceleration = 1.0;
            const accelerationRate = 2.0;
            const decelerationRate = 1.5;

            // Randomly decelerate sometimes to simulate hesitation
            const shouldHesitate = Math.random() < 0.01; // 1% chance per frame

            if (shouldHesitate && this.currentAcceleration > 0.3) {
                // Sudden deceleration (hesitation)
                this.currentAcceleration = Math.max(0.1, this.currentAcceleration - (decelerationRate * 3 * deltaTime));
            } else if (this.idleTimer < this.idleDuration * 0.8) {
                // Normal acceleration during most of the movement
                this.currentAcceleration = Math.min(maxAcceleration, this.currentAcceleration + (accelerationRate * deltaTime));
            } else {
                // Deceleration as we approach the end of this movement segment
                this.currentAcceleration = Math.max(0.1, this.currentAcceleration - (decelerationRate * deltaTime));
            }

            // Move in current direction with acceleration applied
            const wanderSpeed = this.enemyData.speed * 0.5 * this.currentAcceleration; // Slower than normal movement
            const wanderMove = this.moveDirection.clone().multiplyScalar(wanderSpeed * deltaTime);

            // Add slight randomness to movement (like a human's imperfect control)
            wanderMove.x += (Math.random() - 0.5) * 0.005;
            wanderMove.z += (Math.random() - 0.5) * 0.005;

            // Apply movement
            this.enemy.position.add(wanderMove);

            // Face in movement direction with slight delay/lag (more human-like)
            const targetPosition = this.enemy.position.clone().add(this.moveDirection);
            this._faceTargetOrganic(targetPosition, deltaTime);
        } else {
            // Standing still, occasionally look around
            if (this.idleTimer >= this.idleDuration) {
                // Choose new random direction to face and duration
                const angle = Math.random() * Math.PI * 2;
                const lookDir = new THREE.Vector3(Math.cos(angle), 0, Math.sin(angle));

                // Add slight randomness to look direction
                lookDir.x += (Math.random() - 0.5) * 0.2;
                lookDir.z += (Math.random() - 0.5) * 0.2;
                lookDir.normalize();

                // Store the target to look at
                this.currentLookTarget = this.enemy.position.clone().add(lookDir);

                // More varied durations
                this.idleDuration = this._getScaledValue(2, 0.7, 4.0);
                this.idleTimer = 0;

                // Add occasional head tilt (looking up/down slightly)
                this.lookYOffset = (Math.random() - 0.5) * 0.3;

                // Occasionally switch between wandering and standing with higher chance
                if (Math.random() < 0.4) {
                    this.shouldWander = !this.shouldWander;
                }
            }

            // Gradually turn to face target with human-like movement
            if (this.currentLookTarget) {
                // Add slight vertical movement to simulate breathing
                const breathingOffset = Math.sin(this.idleTimer * 1.5) * 0.005;
                this.enemy.position.y += breathingOffset;

                // Organic facing with slight delay
                this._faceTargetOrganic(this.currentLookTarget, deltaTime);
            }
        }
    }
}
