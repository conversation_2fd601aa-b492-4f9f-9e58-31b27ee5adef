/**
 * AI Factory
 * Creates AI brain instances based on enemy type and difficulty
 */
import { AI_BRAIN_TYPES } from '../entities/EnemyTypes.js';
import { RangedCombatAI } from './brains/RangedCombatAI.js';
import { MeleeCombatAI } from './brains/MeleeCombatAI.js';
import { MirrorCombatAI } from './brains/MirrorCombatAI.js';
import { AssassinCombatAI } from './brains/AssassinCombatAI.js';
import { FleeingCombatAI } from './brains/FleeingCombatAI.js';
import { FlyingCombatAI } from './brains/FlyingCombatAI.js';
import { BossCombatAI } from './brains/BossCombatAI.js';

/**
 * Create an AI brain for an enemy
 * @param {Object} enemy - The enemy object
 * @param {Object} enemyData - The enemy data (from userData)
 * @param {Object} scene - The scene object
 * @param {Object} player - The player object
 * @returns {Object} - The AI brain instance
 */
export function createAIBrain(enemy, enemyData, scene, player) {
    // Get AI type and difficulty
    const aiType = enemyData.aiType || AI_BRAIN_TYPES.RANGED; // Default to ranged
    const difficulty = enemyData.difficulty || 1; // Default to difficulty 1

    // Create brain based on AI type
    switch (aiType) {
        case AI_BRAIN_TYPES.RANGED:
            return new RangedCombatAI(enemy, enemyData, scene, player, difficulty);

        case AI_BRAIN_TYPES.MELEE:
            return new MeleeCombatAI(enemy, enemyData, scene, player, difficulty);

        case AI_BRAIN_TYPES.MIRROR:
            return new MirrorCombatAI(enemy, enemyData, scene, player, difficulty);

        case AI_BRAIN_TYPES.ASSASSIN:
            return new AssassinCombatAI(enemy, enemyData, scene, player, difficulty);

        case AI_BRAIN_TYPES.FLEEING:
            return new FleeingCombatAI(enemy, enemyData, scene, player, difficulty);

        case AI_BRAIN_TYPES.FLYING:
            return new FlyingCombatAI(enemy, enemyData, scene, player, difficulty);

        case AI_BRAIN_TYPES.BOSS:
            return new BossCombatAI(enemy, enemyData, scene, player, difficulty);

        default:
            console.warn(`Unknown AI type: ${aiType}, falling back to RangedCombatAI`);
            return new RangedCombatAI(enemy, enemyData, scene, player, difficulty);
    }
}

/**
 * Update AI brain
 * @param {Object} aiBrain - The AI brain instance
 * @param {Number} deltaTime - Time since last update
 * @param {Array} collisionObjects - Objects to check for collision
 * @param {Object} floorBounds - Bounds of the floor
 * @returns {Object} - Updated state data
 */
export function updateAIBrain(aiBrain, deltaTime, collisionObjects, floorBounds) {
    if (!aiBrain) return null;

    // Update brain
    return aiBrain.update(deltaTime, collisionObjects, floorBounds);
}

/**
 * Apply knockback to an enemy
 * @param {Object} aiBrain - The AI brain instance
 * @param {THREE.Vector3} direction - Direction of knockback
 * @param {Number} strength - Strength of knockback
 */
export function applyKnockback(aiBrain, direction, strength) {
    if (!aiBrain) return;

    // Apply knockback
    aiBrain.applyKnockback(direction, strength);
}
