import * as THREE from 'three';
import { STATE } from '../constants.js';
import AudioManager from '../utils/audioManager.js'; // Make sure AudioManager is imported if used directly
import { DungeonGenerator } from '../generators/DungeonGenerator.js'; // <<< Import DungeonGenerator
import { createElementalPlayerModel } from '../generators/prefabs/elementalPlayer.js'; // <<< UPDATED PATH
// Import utils later (textUtils)

// Constants previously in main.js (Consider moving to constants.js)
const originalIntroDialogue = [
    "Huh? Oh!",
    "Didn't see you there... you traveled far, huh?",
    "Come and warm yourself by my flames.", // Trigger for walk
    "..."
];
const fireRevealDialogue = [
    "Ahem. Down here, sparky.",
    "Yeah, the fire. Don't look so surprised, happens all the time.",
    "Name's Flicker. Think of me as... management. I offer *opportunities*.",
    "Flicker: Fancy a change of scenery? A new life? Less... *this*?",
    "Flicker: I can make that happen. Just need some details for your... next incarnation.",
    "Flicker: So, tell <PERSON>lick<PERSON> all about the 'you' you wanna be..."
];
const questions_data = [
    { text: "Where do you awaken?", options: ["Mystic Woods", "Desert Ruins", "Starlit Peaks"], flicker_replies: ["Flicker: Ah, the Mystic Woods...", "Flicker: The Desert Ruins...", "Flicker: The Starlit Peaks!"] },
    { text: "Who stands by your side?", options: ["Fire Spirit", "Shadow Cat", "Sky Drone"], flicker_replies: ["Flicker: Drawn to passion...", "Flicker: One who walks unseen...", "Flicker: A machine? Cold logic..."] },
    { text: "What power flows through you?", options: ["Flame Burst", "Ice Veil", "Thunder Strike"], flicker_replies: ["Flicker: Raw, untamed energy...", "Flicker: Seeking control, then?", "Flicker: Sudden impact..."] },
    { text: "What drives your journey?", options: ["Revenge", "Discovery", "Honor"], flicker_replies: ["Flicker: Such a potent fuel, Revenge.", "Flicker: An endless thirst...", "Flicker: Ah, Honor."] },
    { text: "What's your title?", options: ["Lost Wanderer", "Blade Seeker", "Storm Caller"], flicker_replies: ["Flicker: Adrift and searching...", "Flicker: Drawn to the edge...", "Flicker: You would command the tempest..."] },
    { text: "When faced with despair...?", options: ["Ember of Hope", "Spark of Pragmatism", "Coal of Defiance"], flicker_replies: ["Flicker: Clinging to Hope?", "Flicker: Cold, hard calculation...", "Flicker: Defiance! Yes!"] },
    { text: "Price for ultimate knowledge?", options: ["Betraying Oath", "Losing Self", "Sacrificing Innocent"], flicker_replies: ["Flicker: You hesitate to break an Oath?", "Flicker: Afraid of losing your *self*?", "Flicker: Drawing the line..."] },
    { text: "Whispers in doubt?", options: ["Past Failures", "Forbidden Power", "Profound Stillness"], flicker_replies: ["Flicker: Haunted by Failures?", "Flicker: Even in doubt, the lure...", "Flicker: An unnerving Stillness..."] },
    { text: "View on balance?", options: ["Sacred Dance", "Cycle to Manipulate", "Illusion of Chaos"], flicker_replies: ["Flicker: A 'Sacred Dance'...", "Flicker: Manipulating the cycle...", "Flicker: All an illusion...?"] }, // Shortened for brevity
    { text: "Final demand of fate?", options: ["Eternal Peace", "Ascension", "Shatter Destiny"], flicker_replies: ["Flicker: Peace for *all*?", "Flicker: You seek Ascension!", "Flicker: You would Shatter Destiny!"] }
];
// Simple feedback responses (can be expanded)
const feedbackResponses = {
    0: ["Flicker: Mystic Woods... secrets there.", "Flicker: The Desert Ruins, eh? Watch your step.", "Flicker: Starlit Peaks... a lofty beginning!"],
    1: ["Flicker: A Fire Spirit! Full of energy.", "Flicker: Shadow Cat... sneaky.", "Flicker: A Sky Drone? Interesting choice."],
    2: ["Flicker: Flame Burst! Raw power.", "Flicker: Ice Veil... cool and collected.", "Flicker: Thunder Strike! Quick and decisive."],
    3: ["Flicker: Revenge... a heavy burden.", "Flicker: Discovery! The world awaits.", "Flicker: Honor guides your path."],
    4: ["Flicker: Lost Wanderer... seeking something?", "Flicker: Blade Seeker... drawn to conflict.", "Flicker: Storm Caller... you command the elements."],
    5: ["Flicker: Hope? A fragile warmth...", "Flicker: Pragmatism... sensible, perhaps.", "Flicker: Defiance! Yes!"],
    6: ["Flicker: Hesitate to break an Oath?", "Flicker: Afraid of losing yourself?", "Flicker: Drawing the line... complicated."],
    7: ["Flicker: Haunted by Failures?", "Flicker: Forbidden Power sings...", "Flicker: Stillness... an empty void?"],
    8: ["Flicker: Sacred Dance... quaint.", "Flicker: Manipulating the cycle... promising!", "Flicker: Illusion? A kindred spirit?"],
    9: ["Flicker: Peace? Noble, if dull.", "Flicker: Ascension! Ambitious.", "Flicker: Shatter Destiny! Bold!"]
};
const outroDialogue = "Flicker: Excellent. Your details are recorded. All that remains is your signature...";

// Camera breathing effect parameters
const breathFrequency = 0.2;
const breathAmplitudeY = 0.03;

class CharacterCreationHandler {
    // --- Define Methods Accessed in Constructor FIRST ---
    onKeyDown(event) {
        // --- Debug Shortcut Check FIRST ---
        if (event.key === 'ArrowDown') { // Check using event.key for consistency
            const now = performance.now();
            if (now - this.lastDebugDownPressTime > this.DEBUG_PRESS_INTERVAL) {
                this.debugDownPressCount = 1;
                // Removed log
            } else {
                this.debugDownPressCount++;
                // Removed log
            }
            this.lastDebugDownPressTime = now;

            if (this.debugDownPressCount >= 5) {
                console.log("Debug shortcut triggered!");
                this._triggerDebugDungeonTransition(); // Call the method defined below
                this.debugDownPressCount = 0; // Reset after triggering
                event.preventDefault(); // Prevent normal ArrowDown processing
                return; // Stop further execution in this handler
            }
            // If not triggered, fall through to normal ArrowDown processing below
        } else {
            // Reset count if any *other* key is pressed
            this.debugDownPressCount = 0;
        }
        // --- End Debug Shortcut Check ---

        // If camera is animating, ignore regular input
        if (this.cameraAnimation.active || this.lookAroundAnimation.active) return;

        // Normal Dialogue/Option Navigation
        if (this.dialogueContainer && !this.dialogueContainer.classList.contains('hidden')) {
            const options = this.dialogueOptionsContainer?.querySelectorAll('.dialogue-option-button');
            if (event.key === 'ArrowDown') {
                if (options && options.length > 0) {
                    this.highlightedOptionIndex = (this.highlightedOptionIndex + 1) % options.length;
                    this._updateHighlight();
                    event.preventDefault();
                }
            } else if (event.key === 'ArrowUp') {
                if (options && options.length > 0) {
                    this.highlightedOptionIndex = (this.highlightedOptionIndex - 1 + options.length) % options.length;
                    this._updateHighlight();
                    event.preventDefault();
                }
            } else if (event.key === 'Enter') {
                event.preventDefault();
                const options = this.dialogueOptionsContainer?.querySelectorAll('.dialogue-option-button');

                // Check if options are currently displayed and one is highlighted
                if (options && options.length > 0 && this.highlightedOptionIndex >= 0) {
                    // Click the highlighted button
                    console.log("[Desktop Enter] Clicking highlighted option.");
                    options[this.highlightedOptionIndex].click();
                } else {
                    // Otherwise (no options shown, or none highlighted), treat as advance click
                    console.log("[Desktop Enter] No options selected, calling onClickAdvance.");
                    this.onClickAdvance();
                }
            }
        }
    }

    // Method bound in constructor
    onClickAdvance() {
         // Simplified log
         // console.log(`Dialogue click: Waiting=${this.isWaitingForAdvanceInput}, PostWalk=${this.isWaitingPostWalkInput}, PostEllipsis=${this.isWaitingPostEllipsisInput}, WaitingVideo=${this.waitingForInputAfterVideo}`);

         // --- Handle Input During Video Playback FIRST ---
         if (this.waitingForInputAfterVideo) {
            const elapsed = this.flickerVideoStartTime ? performance.now() - this.flickerVideoStartTime : Infinity;
            // Removed log
            // Check if 2 seconds (2000 ms) have passed
            if (elapsed >= 2000) {
                 // Removed log
                 this.waitingForInputAfterVideo = false; // Clear the flag
                 this.hideFlickerVideo(); // Hide video and then proceed
             } else {
                 // Removed log
             }
             return; // Always return after handling video input
         }
         // --- End Video Input Check ---

         if (this.currentTypingJob.instance && !this.currentTypingJob.isComplete) {
             // Removed log
             clearTimeout(this.currentTypingJob.timeoutId);
             if (this.chatSoundInterval) clearInterval(this.chatSoundInterval);
             this.chatSoundInterval = null;
             this.audioManager?.stopSound('chat');
             if (this.dialogueTextElement) this.dialogueTextElement.textContent = this.currentTypingJob.text;
             this.currentTypingJob.isComplete = true;
             const callback = this.currentTypingJob.callback;
             this.currentTypingJob.instance = null;
             this.currentTypingJob.timeoutId = null;
             this.isWaitingForAdvanceInput = true;
             // Removed log
             return;
         }
         if (this.isWaitingPostWalkInput) {
             this.handlePostWalkAdvance();
         } else if (this.isWaitingPostEllipsisInput) {
             this.handlePostEllipsisAdvance();
         } else if (this.isWaitingForAdvanceInput) {
              // Removed log
              this.isWaitingForAdvanceInput = false;
              this._executeAdvanceCallback();
         }
     }

    // --- Constructor ---
    constructor(sceneManager) {
        this.sceneManager = sceneManager;
        this.audioManager = sceneManager.audioManager;
        this.scene = null;
        // Removed unused this.font assignment

        this.questions_data = questions_data; // Assign the constant data here

        // DOM Elements
        this.dialogueContainer = document.getElementById('dialogue-container');
        this.dialogueTextElement = document.getElementById('dialogue-text');
        this.dialogueOptionsContainer = document.getElementById('dialogue-options');
        this.dialogueProgressElement = document.getElementById('dialogue-progress');
        // Video elements
        this.videoOverlay = document.getElementById('video-overlay');
        this.introVideo = document.getElementById('intro-video');
        this.rebornVideo = document.getElementById('reborn-video');

        // State
        this.userAnswers = [];
        this.currentQuestionIndex = 0;
        this.currentOriginalDialogueIndex = 0;
        this.currentFireDialogueIndex = -1;
        this.highlightedOptionIndex = -1;
        this.isWaitingForAdvanceInput = false;
        this.isWaitingPostWalkInput = false;
        this.isWaitingPostEllipsisInput = false;
        this.waitingForInputAfterVideo = false; // Flag for when flicker video is playing
        this.flickerVideoStartTime = null; // Timestamp for flicker video start
        this.hasIntroChatSoundPlayed = false;
        this.currentTypingJob = { text: '', index: 0, speed: 50, callback: null, instance: null, isComplete: false, timeoutId: null };
        this.chatSoundInterval = null;

        // 3D Objects & Environment
        this.environmentObjects = [];
        this.fireGroup = null;
        this.campfireLight = null;

        // Animations
        this.cameraAnimation = { active: false, startTime: 0, duration: 3000, startPosition: new THREE.Vector3(), endPosition: new THREE.Vector3(0, 1.8, 4.5), startLookAt: new THREE.Vector3(), endLookAt: new THREE.Vector3(0, 0.5, 0) };
        this.lookAroundAnimation = { active: false, startTime: 0, phase: 0, durationLeft: 800, durationRight: 1200, durationCenter: 500, pauseDuration: 200, maxAngleY: Math.PI / 6, targetLookAt: new THREE.Vector3() };
        this.fireDanceAnimation = { active: false, startTime: 0, duration: 800, maxScaleY: 1.3, minScaleXZ: 0.8 };
        this.obfuscationInterval = null;

        // Interaction
        this.raycaster = new THREE.Raycaster();
        this.mouse = new THREE.Vector2();

        // Bound event listeners - NOW `this.onKeyDown` and `this.onClickAdvance` are defined above
        this.boundOnKeyDown = this.onKeyDown.bind(this);
        this.boundOnClickAdvance = this.onClickAdvance.bind(this);

        // Store original global light intensities
        this.originalGlobalAmbientIntensity = 0.5;
        this.originalGlobalDirectionalIntensity = 0.5;

        // Debugging shortcut state
        this.debugDownPressCount = 0;
        this.lastDebugDownPressTime = 0;
        this.DEBUG_PRESS_INTERVAL = 500; // Max ms between presses for sequence
    }

    async init(scene) {
        console.log("CharacterCreationHandler: Initializing...");
        this.scene = scene;
        this.scene.background = new THREE.Color(0x050a20); // Darker night sky for more atmosphere
        this.scene.fog = new THREE.FogExp2(0x050a20, 0.03); // Darker fog with increased density for more dramatic effect

        // --- Dim Global Lights ---
        const globalAmbLight = this.sceneManager.scene.getObjectByName("globalAmbientLight");
        const globalDirLight = this.sceneManager.scene.getObjectByName("globalDirectionalLight");
        if (globalAmbLight) {
            this.originalGlobalAmbientIntensity = globalAmbLight.intensity; // Store original
            globalAmbLight.intensity = 0; // Make it very dim or off for this scene
        }
        if (globalDirLight) {
            this.originalGlobalDirectionalIntensity = globalDirLight.intensity; // Store original
            globalDirLight.intensity = 0; // Turn off directional light
        }
        // --- End Dim Global Lights ---

        // Reset state
        this.userAnswers = [];
        this.currentQuestionIndex = 0;
        this.currentOriginalDialogueIndex = 0;
        this.currentFireDialogueIndex = -1;
        this.highlightedOptionIndex = -1;
        this.isWaitingForAdvanceInput = false;
        this.isWaitingPostWalkInput = false;
        this.isWaitingPostEllipsisInput = false;
        this.waitingForInputAfterVideo = false; // Reset on init
        this.flickerVideoStartTime = null; // Reset on init
        this.hasIntroChatSoundPlayed = false;
        this.cameraAnimation.active = false;
        this.lookAroundAnimation.active = false;
        this.fireDanceAnimation.active = false;

        if (!this.font) {
            console.warn("CharacterCreationHandler: Font not available on init.");
        }

        this._createEnvironment(); // Adds scene-specific lights
        this._setupCamera();

        if (this.dialogueContainer) this.dialogueContainer.classList.remove('hidden');
        if (this.dialogueTextElement) this.dialogueTextElement.textContent = '';
        if (this.dialogueOptionsContainer) this.dialogueOptionsContainer.innerHTML = '';
        if (this.dialogueProgressElement) this.dialogueProgressElement.textContent = '';

        this._addEventListeners();

        this.audioManager?.playSound('bg_ambient_surround', true, 0.2);

        setTimeout(() => {
            this.startEnvironmentIntroDialogue();
        }, 50);

        console.log("CharacterCreationHandler: Initialized.");
    }

    cleanup() {
        console.log("CharacterCreationHandler: Cleaning up...");
        this._removeEventListeners();

        // Hide dialogue and video overlay immediately
        if (this.dialogueContainer) this.dialogueContainer.classList.add('hidden');
        if (this.videoOverlay) {
             this.videoOverlay.classList.remove('visible', 'fading-in');
             this.videoOverlay.style.opacity = 0; // Ensure it's visually gone
        }

        // Stop any active animations or sounds
        this.cameraAnimation.active = false;
        this.lookAroundAnimation.active = false;
        this.fireDanceAnimation.active = false;
        this.audioManager?.stopSound('bg_ambient_surround');
        this.audioManager?.stopSound('chat');
        if (this.chatSoundInterval) clearInterval(this.chatSoundInterval);
        this.chatSoundInterval = null;
        if (this.obfuscationInterval) clearInterval(this.obfuscationInterval);
        this.obfuscationInterval = null;

        // Stop video if playing
        if (this.introVideo && !this.introVideo.paused) {
            this.introVideo.pause();
        }

        // Restore global lights
        const globalAmbLight = this.sceneManager.scene?.getObjectByName("globalAmbientLight");
        const globalDirLight = this.sceneManager.scene?.getObjectByName("globalDirectionalLight");
        if (globalAmbLight) globalAmbLight.intensity = this.originalGlobalAmbientIntensity;
        if (globalDirLight) globalDirLight.intensity = this.originalGlobalDirectionalIntensity;

        // Remove objects from scene (basic cleanup)
        // TODO: Implement proper disposal of geometries, materials, textures
        this.environmentObjects.forEach(obj => this.scene?.remove(obj));
        if (this.fireGroup) this.scene?.remove(this.fireGroup);
        this.environmentObjects = [];
        this.fireGroup = null;
        this.campfireLight = null;

        // Nullify DOM references (though they persist in the actual DOM)
        this.dialogueContainer = null;
        this.dialogueTextElement = null;
        this.dialogueOptionsContainer = null;
        this.dialogueProgressElement = null;
        this.videoOverlay = null;
        this.introVideo = null;
        this.rebornVideo = null;

        console.log("CharacterCreationHandler: Cleaned up.");
    }

    _addEventListeners() {
        // Correct binding, relies on constructor correctly setting boundOnKeyDown/boundOnClickAdvance
        window.addEventListener('keydown', this.boundOnKeyDown, false);
        this.dialogueContainer?.addEventListener('click', this.boundOnClickAdvance);
    }

    _removeEventListeners() {
        // Correct removal
        window.removeEventListener('keydown', this.boundOnKeyDown, false);
        this.dialogueContainer?.removeEventListener('click', this.boundOnClickAdvance);
    }

    _setupCamera() {
        this.sceneManager.camera.position.set(0, 2.5, 12);
        this.sceneManager.camera.lookAt(0, 0.5, 0);
        this.sceneManager.camera.rotation.set(0, 0, 0);
    }

    _createEnvironment() {
        // This function should add the dim blue questAmbientLight and the campfireLight
        // Ensure it's not adding *another* global light
        console.log("Creating questionnaire environment...");
        this.environmentObjects = [];

        // Darker ambient light for more atmospheric mood
        const ambientLight = new THREE.AmbientLight(0x3040a0, 0.4); // Reduced intensity and more blue tint
        ambientLight.name = "questAmbientLight";
        this.scene.add(ambientLight);
        this.environmentObjects.push(ambientLight);

        // Add a hemisphere light with stronger contrast
        const hemiLight = new THREE.HemisphereLight(0xffaa60, 0x101840, 0.5); // Warmer top color, darker bottom color
        hemiLight.name = "questHemiLight";
        this.scene.add(hemiLight);
        this.environmentObjects.push(hemiLight);

        // More intense campfire light with shorter range for dramatic effect
        this.campfireLight = new THREE.PointLight(0xff8040, 6.0, 25); // Increased intensity, warmer color, shorter range
        this.campfireLight.position.set(0, 0.5, 0);
        this.campfireLight.castShadow = true;
        this.campfireLight.shadow.radius = 2;
        this.campfireLight.shadow.mapSize.width = 1024;
        this.campfireLight.shadow.mapSize.height = 1024;
        this.campfireLight.name = "campfireLight";
        this.scene.add(this.campfireLight);
        this.environmentObjects.push(this.campfireLight);

        // Subtle fill light to provide minimal illumination to distant areas
        const fillLight = new THREE.DirectionalLight(0xffffbb, 0.3); // Reduced intensity
        fillLight.position.set(5, 10, 5);
        fillLight.name = "questFillLight";
        this.scene.add(fillLight);
        this.environmentObjects.push(fillLight);

        // Ground Plane
        const groundSize = 80;
        const segments = 40;
        const groundGeometry = new THREE.PlaneGeometry(groundSize, groundSize, segments, segments);
        const positions = groundGeometry.attributes.position;
        const colors = [];
        const colorBrown = new THREE.Color(0x3c2a1a);
        const colorDarkGreen = new THREE.Color(0x1a4d2e);
        for (let i = 0; i < positions.count; i++) {
            const x = positions.getX(i); const z = positions.getZ(i);
            const displacement = (Math.sin(x * 0.2) + Math.cos(z * 0.2)) * 0.15 + Math.random() * 0.1;
            positions.setY(i, positions.getY(i) + displacement);
            let color = colorBrown.clone();
            if (Math.random() < 0.15) color = colorDarkGreen.clone();
            color.multiplyScalar(THREE.MathUtils.mapLinear(displacement, -0.2, 0.3, 0.8, 1.1));
            colors.push(color.r, color.g, color.b);
        }
        groundGeometry.computeVertexNormals();
        groundGeometry.setAttribute('color', new THREE.Float32BufferAttribute(colors, 3));
        const groundMaterial = new THREE.MeshStandardMaterial({ roughness: 0.95, metalness: 0.1, vertexColors: true });
        const ground = new THREE.Mesh(groundGeometry, groundMaterial);
        ground.rotation.x = -Math.PI / 2; ground.position.y = -1.5; ground.receiveShadow = true; ground.name = "ground";
        this.scene.add(ground);
        this.environmentObjects.push(ground);

        // Starfield
        const starGeometry = new THREE.BufferGeometry();
        const starMaterial = new THREE.PointsMaterial({ color: 0xffffff, size: 0.05, transparent: true, opacity: 0.8 });
        const starVertices = [];
        for (let i = 0; i < 5000; i++) {
            const x = THREE.MathUtils.randFloatSpread(200); const y = THREE.MathUtils.randFloatSpread(200); const z = THREE.MathUtils.randFloatSpread(200);
            if (Math.sqrt(x*x + y*y + z*z) > 30) starVertices.push(x, y, z);
        }
        starGeometry.setAttribute('position', new THREE.Float32BufferAttribute(starVertices, 3));
        const stars = new THREE.Points(starGeometry, starMaterial); stars.name = "stars";
        this.scene.add(stars);
        this.environmentObjects.push(stars);

        // Trees (Simplified)
        const treeLeafColors = [new THREE.Color(0x1a4d2e), new THREE.Color(0x225522), new THREE.Color(0x2e6b3a)];
        const treeTrunkColors = [new THREE.Color(0x6a4f3a), new THREE.Color(0x5c4431), new THREE.Color(0x785b46)];
        const leafMaterial = new THREE.MeshStandardMaterial({ roughness: 0.85, metalness: 0.1, vertexColors: true });
        const leafBlockGeo = new THREE.BoxGeometry(0.4, 0.4, 0.4);
        for (let i = 0; i < 30; i++) {
            const treeGroup = new THREE.Group();
            const trunkHeight = THREE.MathUtils.randFloat(2.0, 5.0); const trunkRadius = THREE.MathUtils.randFloat(0.2, 0.4);
            const trunkColorBase = treeTrunkColors[Math.floor(Math.random() * treeTrunkColors.length)];
            const trunkGeometry = new THREE.CylinderGeometry(trunkRadius * 0.8, trunkRadius, trunkHeight, 8);
            const trunkColors = []; const trunkPositions = trunkGeometry.attributes.position;
            for (let j = 0; j < trunkPositions.count; j++) {
                const y = trunkPositions.getY(j);
                const color = trunkColorBase.clone();
                color.multiplyScalar(THREE.MathUtils.mapLinear(y, -trunkHeight/2, trunkHeight/2, 0.7, 1.1) * THREE.MathUtils.randFloat(0.95, 1.05));
                trunkColors.push(color.r, color.g, color.b);
            }
            trunkGeometry.setAttribute('color', new THREE.Float32BufferAttribute(trunkColors, 3));
            const trunkMaterial = new THREE.MeshStandardMaterial({ roughness: 0.9, metalness: 0.1, vertexColors: true });
            const trunk = new THREE.Mesh(trunkGeometry, trunkMaterial); trunk.position.y = trunkHeight / 2; trunk.castShadow = true;
            treeGroup.add(trunk);
            const leafCluster = new THREE.Group();
            const leafBlockCount = THREE.MathUtils.randInt(40, 80);
            const leafColorBase = treeLeafColors[Math.floor(Math.random() * treeLeafColors.length)];
            for (let l = 0; l < leafBlockCount; l++) {
                 const blockMesh = new THREE.Mesh(leafBlockGeo.clone(), leafMaterial.clone());
                 const scale = THREE.MathUtils.randFloat(0.7, 1.1); blockMesh.scale.setScalar(scale);
                 const radius = trunkHeight * 0.4 * Math.pow(Math.random(), 0.5);
                 const phi = Math.acos(1 - 2 * Math.random()); const theta = Math.random() * Math.PI * 2;
                 blockMesh.position.setFromSphericalCoords(radius, phi, theta); blockMesh.position.y += trunkHeight * 0.9;
                 blockMesh.rotation.set(Math.random() * Math.PI, Math.random() * Math.PI, Math.random() * Math.PI);
                 blockMesh.castShadow = true;
                 const blockColors = [];
                 for (let k = 0; k < blockMesh.geometry.attributes.position.count; k++) {
                    const color = leafColorBase.clone(); color.multiplyScalar(THREE.MathUtils.randFloat(0.9, 1.1)); blockColors.push(color.r, color.g, color.b);
                 }
                 blockMesh.geometry.setAttribute('color', new THREE.Float32BufferAttribute(blockColors, 3));
                 leafCluster.add(blockMesh);
            }
            treeGroup.add(leafCluster);
            const angle = Math.random() * Math.PI * 2; const radius = THREE.MathUtils.randFloat(15, 35);
            treeGroup.position.set(Math.cos(angle) * radius, ground.position.y, Math.sin(angle) * radius);
            treeGroup.rotation.y = Math.random() * Math.PI * 2;
            treeGroup.userData.swaySpeed = Math.random() * 0.004 + 0.001; treeGroup.userData.swayAmount = Math.random() * 0.008 + 0.003;
            treeGroup.name = `tree_${i}`;
            this.scene.add(treeGroup);
            this.environmentObjects.push(treeGroup);
        }

        // Logs
        const logColorBase = new THREE.Color(0x8b4513);
        const logGeometry = new THREE.CylinderGeometry(0.1, 0.12, 1.5, 8);
        const logPositionsArr = logGeometry.attributes.position; const logColors = [];
        for(let j=0; j<logPositionsArr.count; j++) {
            const y = logPositionsArr.getY(j); const color = logColorBase.clone(); const endFade = Math.abs(y) / (1.5 / 2);
            color.multiplyScalar(THREE.MathUtils.mapLinear(endFade, 0, 1, 1.0, 0.6)); logColors.push(color.r, color.g, color.b);
        }
        logGeometry.setAttribute('color', new THREE.Float32BufferAttribute(logColors, 3));
        const logMaterial = new THREE.MeshStandardMaterial({ roughness: 0.85, metalness: 0.1, vertexColors: true });
        const campfireLogGroup = new THREE.Group();
        const logPositionsData = [
            { pos: [0, 0.1, 0.4], rot: [Math.PI / 2, 0, Math.PI / 6] }, { pos: [0.3, 0.1, -0.2], rot: [Math.PI / 2, 0, -Math.PI / 8] },
            { pos: [-0.3, 0.1, -0.2], rot: [Math.PI / 2, 0, Math.PI / -9] }, { pos: [0, 0.3, 0], rot: [Math.PI / 2 + 0.1, 0, Math.PI / 2] }
        ];
        logPositionsData.forEach(p => {
            const log = new THREE.Mesh(logGeometry, logMaterial); log.position.set(...p.pos); log.rotation.set(...p.rot); log.castShadow = true;
            campfireLogGroup.add(log);
        });
        campfireLogGroup.position.y = ground.position.y; campfireLogGroup.scale.setScalar(1.1); campfireLogGroup.name = "campfireLogs";
        this.scene.add(campfireLogGroup);
        this.environmentObjects.push(campfireLogGroup);

        // Fire - enhanced with more vibrant colors and emissive materials
        this.fireGroup = new THREE.Group(); this.fireGroup.name = "fireGroup";
        // More vibrant and varied fire colors
        const fireColorsArr = [
            new THREE.Color(0xff0000), // Red
            new THREE.Color(0xff3000), // Orange-red
            new THREE.Color(0xff6000), // Orange
            new THREE.Color(0xff9000), // Amber
            new THREE.Color(0xffb000), // Gold
            new THREE.Color(0xffd700), // Yellow
            new THREE.Color(0xffffff)  // White (for the hottest parts)
        ];
        const fireBlockGeo = new THREE.BoxGeometry(0.1, 0.1, 0.1);
        const totalParticles = 180; // Increased number of particles for a more dramatic fire effect
        for (let i = 0; i < totalParticles; i++) {
            // Randomly select emissive color for more varied fire appearance
            const emissiveColor = fireColorsArr[Math.floor(Math.random() * fireColorsArr.length)];
            const fireMaterial = new THREE.MeshStandardMaterial({
                vertexColors: true,
                emissive: emissiveColor,
                emissiveIntensity: 2.0, // Increased emissive intensity for more glow
                roughness: 0.1,
                metalness: 0.0
            });
            const fireParticle = new THREE.Mesh(fireBlockGeo.clone(), fireMaterial);
            const particleColors = []; const baseColor = fireColorsArr[Math.floor(Math.random() * fireColorsArr.length)];
            for(let k=0; k<fireParticle.geometry.attributes.position.count; k++) {
                const color = baseColor.clone(); color.multiplyScalar(THREE.MathUtils.randFloat(0.9, 1.5)); particleColors.push(color.r, color.g, color.b);
            }
            fireParticle.geometry.setAttribute('color', new THREE.Float32BufferAttribute(particleColors, 3));
            const heightRatio = Math.random(); const maxRadius = 0.25; const radius = maxRadius * (1.0 - heightRatio * heightRatio); const angle = Math.random() * Math.PI * 2;
            const height = THREE.MathUtils.randFloat(0.1, 0.7);
            fireParticle.position.set(Math.cos(angle) * radius, height, Math.sin(angle) * radius);
            fireParticle.userData.initialPos = fireParticle.position.clone();
            fireParticle.userData.speedY = THREE.MathUtils.randFloat(0.05, 0.2);
            fireParticle.userData.life = THREE.MathUtils.randFloat(0.4, 1.4);
            fireParticle.userData.age = Math.random() * fireParticle.userData.life;
            fireParticle.userData.oscillationSpeed = THREE.MathUtils.randFloat(2.0, 4.0);
            fireParticle.userData.oscillationAmplitude = THREE.MathUtils.randFloat(0.05, 0.15);
            this.fireGroup.add(fireParticle);
        }
        this.fireGroup.position.y = ground.position.y; this.fireGroup.position.z = 0.15;
        this.fireGroup.userData.basePositionY = this.fireGroup.position.y;
        this.scene.add(this.fireGroup);
        console.log("Environment created.");
    }

    update(deltaTime, scene, camera) {
        const time = this.sceneManager.clock.getElapsedTime();
        this._updateFireAnimation(deltaTime, time);
        this._updateTreeAnimation(deltaTime, time);
        this._updateCameraWalkAnimation(deltaTime, time, camera);
        this._updateLookAroundAnimation(deltaTime, time, camera);
        this._updateFireDanceAnimation(deltaTime, time);
        if (!this.cameraAnimation.active && !this.lookAroundAnimation.active) {
            this._updateCameraBreathing(deltaTime, time, camera);
        }
    }

    _updateFireAnimation(deltaTime, time) {
        if (!this.fireGroup) return;
        const groundLevel = -1.5;
        const maxFlameHeight = groundLevel + 2.3;
        this.fireGroup.children.forEach(particle => {
            particle.userData.age += deltaTime;
            if (particle.userData.age >= particle.userData.life) {
                particle.position.copy(particle.userData.initialPos);
                particle.position.x += THREE.MathUtils.randFloatSpread(0.1);
                particle.position.z += THREE.MathUtils.randFloatSpread(0.1);
                particle.userData.age = 0;
                particle.scale.setScalar(1.0);
            } else {
                const lifeRatio = particle.userData.age / particle.userData.life;
                const ageSlowdown = Math.max(0, 1.0 - lifeRatio * lifeRatio);
                const currentSpeedY = particle.userData.speedY * ageSlowdown;
                particle.position.y += currentSpeedY * deltaTime;
                const heightRatioDrift = Math.max(0, (particle.position.y - groundLevel) / 2.3);
                const driftStrength = heightRatioDrift * 0.04;
                const dx = -particle.position.x; const dz = -particle.position.z;
                const distance = Math.sqrt(dx * dx + dz * dz);
                if (distance > 0.01) {
                    particle.position.x += (dx / distance) * driftStrength * deltaTime;
                    particle.position.z += (dz / distance) * driftStrength * deltaTime;
                }
                particle.position.x += Math.sin(time * particle.userData.oscillationSpeed) * particle.userData.oscillationAmplitude * 0.3 * deltaTime;
                particle.position.z += Math.cos(time * particle.userData.oscillationSpeed) * particle.userData.oscillationAmplitude * 0.3 * deltaTime;
                if (particle.position.y > maxFlameHeight) {
                    particle.position.copy(particle.userData.initialPos);
                    particle.userData.age = 0;
                }
                const shrinkFactor = Math.max(0.05, 1.0 - lifeRatio * 0.7);
                particle.scale.setScalar(shrinkFactor);
            }
        });
        if (this.campfireLight) {
            // More dramatic flickering for the campfire light
            const flicker = Math.sin(time * 5) * 1.2 + Math.sin(time * 7.3) * 0.6 + Math.random() * 1.0;
            this.campfireLight.intensity = 6.0 + flicker; // More intense base with stronger variation

            // Slightly vary the color temperature for a more realistic fire effect
            const colorTemp = 0.05 * Math.sin(time * 3.7);
            this.campfireLight.color.r = 1.0;
            this.campfireLight.color.g = 0.5 + colorTemp;
            this.campfireLight.color.b = 0.25 + colorTemp * 0.5;
        }
    }

    _updateTreeAnimation(deltaTime, time) {
        this.environmentObjects.forEach(obj => {
            if (obj.name.startsWith('tree_')) {
                obj.rotation.z = Math.sin(time * obj.userData.swaySpeed) * obj.userData.swayAmount * 0.5;
                obj.rotation.x = Math.cos(time * obj.userData.swaySpeed * 0.7) * obj.userData.swayAmount * 0.25;
                const leafCluster = obj.children.find(child => child instanceof THREE.Group);
                 if(leafCluster){
                      leafCluster.children.forEach(leafBlock => {
                           if (leafBlock instanceof THREE.Mesh) {
                                leafBlock.rotation.x += Math.sin(time * 5 + leafBlock.position.x) * 0.05 * deltaTime;
                                leafBlock.rotation.y += Math.cos(time * 4 + leafBlock.position.z) * 0.04 * deltaTime;
                           }
                      });
                  }
            }
        });
    }

     _updateCameraWalkAnimation(deltaTime, time, camera) {
         if (!this.cameraAnimation.active) return;
         const elapsed = this.sceneManager.clock.getElapsedTime() - this.cameraAnimation.startTime;
         let progress = Math.min(elapsed / (this.cameraAnimation.duration / 1000), 1);
         progress = progress * progress * (3 - 2 * progress);
         camera.position.lerpVectors(this.cameraAnimation.startPosition, this.cameraAnimation.endPosition, progress);
         const stepFrequency = progress * Math.PI * 2 * 3.5;
         const bobbleAmountY = 0.07;
         const swayAmountX = 0.03;
         const currentY = camera.position.y;
         camera.position.y += Math.sin(stepFrequency) * bobbleAmountY;
         camera.position.x += Math.cos(stepFrequency * 0.5) * swayAmountX * (1-progress);
         const currentLookAt = new THREE.Vector3().lerpVectors(this.cameraAnimation.startLookAt, this.cameraAnimation.endLookAt, progress);
         camera.lookAt(currentLookAt);
         if (progress >= 1) {
             console.log("Camera walk animation finished.");
             this.cameraAnimation.active = false;
             camera.position.copy(this.cameraAnimation.endPosition);
             camera.lookAt(this.cameraAnimation.endLookAt);
             camera.rotation.set(0,0,0);
             console.log("Walk finished. Setting isWaitingPostWalkInput = true.");
             this.isWaitingPostWalkInput = true;
             if(this.dialogueTextElement) this.dialogueTextElement.textContent = '';
         }
     }

     _updateLookAroundAnimation(deltaTime, time, camera) {
         if (!this.lookAroundAnimation.active) return;
         const elapsed = this.sceneManager.clock.getElapsedTime() - this.lookAroundAnimation.startTime;
         const baseLookAt = this.cameraAnimation.endLookAt;
         let lookProgress = 0;
         switch (this.lookAroundAnimation.phase) {
             case 0:
                 if (elapsed >= this.lookAroundAnimation.pauseDuration / 1000) {
                     this.lookAroundAnimation.phase = 1;
                     this.lookAroundAnimation.startTime = this.sceneManager.clock.getElapsedTime();
                 }
                 break;
             case 1:
                 lookProgress = Math.min(elapsed / (this.lookAroundAnimation.durationLeft / 1000), 1);
                 lookProgress = lookProgress * lookProgress;
                 const angleLeft = THREE.MathUtils.lerp(0, -this.lookAroundAnimation.maxAngleY, lookProgress);
                 this.lookAroundAnimation.targetLookAt.set(baseLookAt.x + Math.sin(angleLeft), baseLookAt.y, baseLookAt.z + Math.cos(angleLeft) - 1);
                 camera.lookAt(this.lookAroundAnimation.targetLookAt);
                 if (lookProgress >= 1) {
                     this.lookAroundAnimation.phase = 2;
                     this.lookAroundAnimation.startTime = this.sceneManager.clock.getElapsedTime();
                 }
                 break;
             case 2:
                 lookProgress = Math.min(elapsed / (this.lookAroundAnimation.durationRight / 1000), 1);
                 lookProgress = 0.5 * (1 - Math.cos(lookProgress * Math.PI));
                 const angleRight = THREE.MathUtils.lerp(-this.lookAroundAnimation.maxAngleY, this.lookAroundAnimation.maxAngleY, lookProgress);
                 this.lookAroundAnimation.targetLookAt.set(baseLookAt.x + Math.sin(angleRight), baseLookAt.y, baseLookAt.z + Math.cos(angleRight) - 1);
                 camera.lookAt(this.lookAroundAnimation.targetLookAt);
                 if (lookProgress >= 1) {
                     this.lookAroundAnimation.phase = 3;
                     this.lookAroundAnimation.startTime = this.sceneManager.clock.getElapsedTime();
                 }
                 break;
             case 3:
                 lookProgress = Math.min(elapsed / (this.lookAroundAnimation.durationCenter / 1000), 1);
                 lookProgress = 1 - (1 - lookProgress) * (1 - lookProgress);
                 const angleCenter = THREE.MathUtils.lerp(this.lookAroundAnimation.maxAngleY, 0, lookProgress);
                 this.lookAroundAnimation.targetLookAt.set(baseLookAt.x + Math.sin(angleCenter), baseLookAt.y, baseLookAt.z + Math.cos(angleCenter) - 1);
                 camera.lookAt(this.lookAroundAnimation.targetLookAt);
                 if (lookProgress >= 1) {
                     this.lookAroundAnimation.active = false;
                     camera.lookAt(baseLookAt);
                     console.log("Look around finished. Starting fire reveal dialogue...");
                     this.startFireRevealDialogue();
                 }
                 break;
         }
     }

     _updateFireDanceAnimation(deltaTime, time) {
         if (!this.fireDanceAnimation.active || !this.fireGroup) return;
         const elapsed = this.sceneManager.clock.getElapsedTime() - this.fireDanceAnimation.startTime;
         let progress = Math.min(elapsed / (this.fireDanceAnimation.duration / 1000), 1);
         const danceProgress = Math.sin(progress * Math.PI);
         const scaleY = THREE.MathUtils.lerp(1.0, this.fireDanceAnimation.maxScaleY, danceProgress);
         const scaleXZ = THREE.MathUtils.lerp(1.0, this.fireDanceAnimation.minScaleXZ, danceProgress);
         const basePositionY = this.fireGroup.userData.basePositionY;
         const scaledPositionY = basePositionY + (scaleY - 1.0) * 0.5;
         this.fireGroup.scale.set(scaleXZ, scaleY, scaleXZ);
         this.fireGroup.position.y = scaledPositionY;
         if (progress >= 1) {
             this.fireDanceAnimation.active = false;
             this.fireGroup.scale.set(1, 1, 1);
             this.fireGroup.position.y = basePositionY;
         }
     }

     _updateCameraBreathing(deltaTime, time, camera) {
         const baseY = this.cameraAnimation.endPosition.y;
         const breathOffset = Math.sin(time * Math.PI * 2 * breathFrequency) * breathAmplitudeY;
         if (Math.abs(camera.position.y - baseY) < breathAmplitudeY * 2) {
            camera.position.y = baseY + breathOffset;
         }
     }

    startEnvironmentIntroDialogue() {
        console.log("Starting environment intro dialogue...");
        this.currentOriginalDialogueIndex = 0;
        this.currentFireDialogueIndex = -1;
        this.typeDialogueLine(originalIntroDialogue[this.currentOriginalDialogueIndex], this.nextEnvironmentDialogueLine.bind(this), 50, 'env');
    }

    typeDialogueLine(text, callback, speed = 50, typeInstanceIdentifier = 'default') {
         clearTimeout(this.currentTypingJob.timeoutId);
         if (this.chatSoundInterval) clearInterval(this.chatSoundInterval);
         this.chatSoundInterval = null;
         this.audioManager?.stopSound('chat');
         if (!this.dialogueTextElement) return;
         this.dialogueTextElement.textContent = '';
         let index = 0;
         this.currentTypingJob = { text: text, index: 0, speed: speed, callback: callback, instance: typeInstanceIdentifier, isComplete: false, timeoutId: null };
         if (index === 0) this._tryPlayIntroChatSound();
         this.chatSoundInterval = setInterval(() => {
            this.audioManager?.playSound('chat', false, 0.3);
         }, 110);
         const typeChar = () => {
             if (index < text.length) {
                 this.dialogueTextElement.textContent += text[index];
                 this.currentTypingJob.index = index;
                 index++;
                 this.currentTypingJob.timeoutId = setTimeout(typeChar, speed);
             } else {
                 clearInterval(this.chatSoundInterval);
                 this.chatSoundInterval = null;
                 this.currentTypingJob.isComplete = true;
                 this.currentTypingJob.instance = null;
                 this.currentTypingJob.timeoutId = null;
                 this.isWaitingForAdvanceInput = true;
                 console.log(`Typing finished for [${typeInstanceIdentifier}]: "${text.substring(0,20)}...". Waiting for advance.`);
                 if (callback) {
                      console.log(`Callback ${callback.name || '(anonymous)'} is set for next advance.`);
                 }
             }
         };
         this.currentTypingJob.timeoutId = setTimeout(typeChar, speed);
     }

    _tryPlayIntroChatSound() {
        if (!this.hasIntroChatSoundPlayed) {
            this.audioManager?.playSound('chat', false, 0.3);
            this.hasIntroChatSoundPlayed = true;
        }
    }

    _executeAdvanceCallback() {
         const callback = this.currentTypingJob.callback;
         this.currentTypingJob.callback = null;
         if (callback) {
             console.log(`Executing advance callback: ${callback.name || '(anonymous)'}`);
             callback();
         } else {
             console.warn("Attempted to execute advance callback, but none was set.");
         }
     }

    nextEnvironmentDialogueLine() {
        console.log("Advancing environment dialogue...");
        // Check if the current dialogue line triggers the camera walk
        if (originalIntroDialogue[this.currentOriginalDialogueIndex]?.includes("warm yourself")) {
            console.log("Triggering camera walk animation...");
            // Initiate the animation by setting its state, not calling a missing function
            this.cameraAnimation.active = true;
            this.cameraAnimation.startTime = this.sceneManager.clock.getElapsedTime();
            // Reset start position/lookAt just before starting (optional but safer)
            this.cameraAnimation.startPosition.copy(this.sceneManager.camera.position);
            this.cameraAnimation.startLookAt.set(0, 0.5, 0); // Look slightly above origin initially

            // Potentially play sounds associated with walk start
            const numSteps = 3;
            const totalDuration = this.cameraAnimation.duration;
            const delayBetweenSteps = totalDuration / (numSteps + 1);
            for (let i = 1; i <= numSteps; i++) {
                 setTimeout(() => {
                     this.audioManager?.playSound('footstep');
                 }, delayBetweenSteps * i);
             }
             this.audioManager?.fadeVolume('bg_ambient_surround', 1.0, totalDuration);

             // Prevent advancing dialogue further until animation finishes
             // The animation's update loop will handle the next step (waiting for input)
             return;
        }

        // If not triggering walk, advance to the next line
        this.currentOriginalDialogueIndex++;
        if (this.currentOriginalDialogueIndex < originalIntroDialogue.length) {
            this.typeDialogueLine(originalIntroDialogue[this.currentOriginalDialogueIndex], this.nextEnvironmentDialogueLine.bind(this), 50, 'env');
        } else {
            console.warn("End of original intro reached unexpectedly? Starting fire reveal.");
            this.startFireRevealDialogue();
        }
    }

    startFireRevealDialogue() {
        console.log("Starting fire reveal dialogue...");
        this.currentOriginalDialogueIndex = -1;
        this.currentFireDialogueIndex = 0;
        this.typeDialogueLine(fireRevealDialogue[this.currentFireDialogueIndex], this.nextFireRevealLine.bind(this), 50, 'fire');
    }

    nextFireRevealLine() {
        this.currentFireDialogueIndex++;
        console.log(`[nextFireRevealLine] Advancing fire dialogue. Index is now: ${this.currentFireDialogueIndex}`);

        // --- Trigger Music & Video ---
        if (this.currentFireDialogueIndex === 1) { // <<< Changed condition from 2 to 1
            console.log("[nextFireRevealLine] Index is 1. Starting ambient music and triggering Flicker video..."); // <<< Updated Log
            this.audioManager?.playSound('bg_ambient_music', true, 0.4);
            this.isWaitingForAdvanceInput = false; // Not waiting for text advance
            this.showFlickerVideo();
            console.log("[nextFireRevealLine] showFlickerVideo() called. Returning now.");
            return; // Stop here, video logic will take over
        }
        // --- Original Video Trigger Removed ---
        /*
        if (this.currentFireDialogueIndex === 2) { ... }
        */
         else {
             console.log(`[nextFireRevealLine] Index is ${this.currentFireDialogueIndex} (NOT 1). Proceeding with normal text line.`); // <<< Updated Log
        }
        // --- End Video Trigger Check ---

        if (this.currentFireDialogueIndex < fireRevealDialogue.length) {
            const line = fireRevealDialogue[this.currentFireDialogueIndex];
            this.typeDialogueLine(line, this.nextFireRevealLine.bind(this));
        } else {
            console.log("Fire reveal dialogue finished. Starting questionnaire.");
            this.startQuestionnaire();
        }
    }

    startQuestionnaire() {
        console.log("Starting questionnaire...");
        this.currentQuestionIndex = 0;
        this.userAnswers = [];
        this.displayQuestion(this.currentQuestionIndex);
    }

    displayQuestion(index) {
        console.log(`Displaying question ${index}`);
        if (index >= questions_data.length) {
            this.showFinalPrompt();
            return;
        }
        const questionData = questions_data[index];
        if (this.dialogueProgressElement) this.dialogueProgressElement.textContent = `Question ${index + 1}/${questions_data.length}`;
        if (this.dialogueOptionsContainer) this.dialogueOptionsContainer.innerHTML = '';
        const displayOptionsCallback = () => {
             console.log("Displaying options for question", index);
             if (!this.dialogueOptionsContainer) return;
             this.dialogueOptionsContainer.innerHTML = '';
             this.highlightedOptionIndex = -1;
             questionData.options.forEach((optionText, i) => {
                 const button = document.createElement('button');
                 button.classList.add('dialogue-option-button');
                 button.textContent = `* ${optionText}`;
                 button.addEventListener('mouseenter', () => { this.highlightedOptionIndex = i; this._updateHighlight(); });
                 button.addEventListener('mouseleave', () => { this.highlightedOptionIndex = -1; this._updateHighlight(); });
                 button.onclick = () => this.selectOption(i);
                 this.dialogueOptionsContainer.appendChild(button);
             });
             setTimeout(() => {
                 if (this.dialogueOptionsContainer.children.length > 0) {
                     this.highlightedOptionIndex = 0;
                     this._updateHighlight();
                 }
             }, 0);
             this.isWaitingForAdvanceInput = false;
        };
        this.typeDialogueLine(questionData.text, displayOptionsCallback, 30, 'question');
    }

    _updateHighlight(container = this.dialogueOptionsContainer) {
        if (!container) return;
        const buttons = container.querySelectorAll('.dialogue-option-button');
        buttons.forEach((button, index) => {
            button.classList.toggle('highlighted', index === this.highlightedOptionIndex);
        });
    }

    selectOption(optionIndex) {
        console.log(`Selected option ${optionIndex} for question ${this.currentQuestionIndex}`);
        if (this.currentQuestionIndex >= questions_data.length) return;
        this.highlightedOptionIndex = -1;
        this._updateHighlight();
        this.audioManager?.playSound('select');
        const questionData = questions_data[this.currentQuestionIndex];
        const chosenOptionText = questionData.options[optionIndex];
        this.userAnswers.push({ question: questionData.text, answer: chosenOptionText });
        if (this.dialogueOptionsContainer) this.dialogueOptionsContainer.innerHTML = '';
        if (this.dialogueProgressElement) this.dialogueProgressElement.textContent = '';
        this._startFireDanceAnimation();
        const feedbackText = feedbackResponses[this.currentQuestionIndex]?.[optionIndex] || "Flicker: Hmm...";
        const feedbackCallback = () => {
            this.proceedToNextQuestion();
        };
        this.typeDialogueLine(feedbackText, feedbackCallback, 40, 'feedback');
    }

    proceedToNextQuestion() {
        console.log("Proceeding to next question...");
        this.isWaitingForAdvanceInput = false;
        this.currentQuestionIndex++;
        this.displayQuestion(this.currentQuestionIndex);
    }

    showFinalPrompt() {
        console.log("Showing final prompt...");
        if (this.dialogueProgressElement) this.dialogueProgressElement.textContent = 'Complete!';
        const optionsContainer = document.getElementById('dialogue-options');
        if (optionsContainer) optionsContainer.innerHTML = '';
        this.highlightedOptionIndex = -1;

        const finalPromptCallback = () => {
             console.log("Displaying final button...");
             if (!optionsContainer) return;
             optionsContainer.innerHTML = ''; // Clear just in case
             this.highlightedOptionIndex = -1;
             const button = document.createElement('button');
             button.id = 'final-button-id'; // Assign an ID for hiding later
             button.classList.add('dialogue-option-button');
             button.textContent = '* Get Born';
             button.addEventListener('mouseenter', () => { this.highlightedOptionIndex = 0; this._updateHighlight(optionsContainer); });
             button.addEventListener('mouseleave', () => { this.highlightedOptionIndex = -1; this._updateHighlight(optionsContainer); });
             button.onclick = () => this.finishQuestionnaire(); // <<< Call finishQuestionnaire, NOT triggerDungeonTransition
             optionsContainer.appendChild(button);

             // Need to re-query options after adding the button
             setTimeout(() => {
                  const finalButton = optionsContainer.querySelector('#final-button-id');
                  if (finalButton) {
                      this.highlightedOptionIndex = 0;
                      this._updateHighlight(optionsContainer);
                  }
             }, 0);
              this.isWaitingForAdvanceInput = false;
        };
        this.typeDialogueLine(outroDialogue, finalPromptCallback, 30, 'outro');
    }

    triggerDungeonTransition(answersArray = null) {
        // If no specific answers array is provided (manual flow),
        // extract answers from the stored userAnswers objects.
        let finalAnswers;
        if (answersArray) {
            finalAnswers = answersArray; // Use the provided array (debug flow)
            console.log("Triggering Dungeon Transition with provided answers (debug flow):", finalAnswers);
        } else {
            // Ensure this.userAnswers is an array of objects with 'answer' property
            if (!Array.isArray(this.userAnswers) || this.userAnswers.some(item => typeof item !== 'object' || !item.hasOwnProperty('answer'))) {
                console.error("Manual answers format is incorrect. Expected array of {question, answer} objects.", this.userAnswers);
                this.transitioning = false; // Reset flag if format is wrong
                return; // Prevent transition
            }
            finalAnswers = this.userAnswers.map(item => item.answer);
            console.log("Triggering Dungeon Transition with extracted manual answers:", finalAnswers);
        }

        // Check if transition already started (prevent double trigger)
        if (this.transitioning) return;
        this.transitioning = true;

        this.highlightedOptionIndex = -1;
        this._updateHighlight();
        this.audioManager?.playSound('creepy_noise', false, 0.1);
        this.audioManager?.fadeVolume('bg_ambient_surround', 0, 500);
        this.audioManager?.fadeVolume('bg_ambient_music', 0, 500);

        // Simpler: Hide UI and start fade immediately
        // Use optional chaining for robustness
        this.dialogueManager?.hide();
        document.getElementById('dialogue-options')?.replaceChildren(); // Clear options
        document.getElementById('final-button-id')?.classList.add('hidden'); // Assuming an ID for the final button container
        document.getElementById('dialogue-container')?.classList.add('hidden');

        this.sceneManager.startFade(() => {
            // Double-check finalAnswers format before changing state
            if (!Array.isArray(finalAnswers) || finalAnswers.some(item => typeof item !== 'string')) {
                 console.error("Attempted to change state with invalid answers format:", finalAnswers);
                 this.transitioning = false; // Allow trying again if needed
                 // Potentially revert fade or show an error message
                 return;
            }
            this.sceneManager.changeState(STATE.DUNGEON, { userAnswers: finalAnswers });
        });
    }

    // Ensure _triggerDebugDungeonTransition still calls correctly
     _triggerDebugDungeonTransition() {
         // Ensure questions_data is assigned in the constructor
         if (!this.questions_data || this.questions_data.length === 0) {
             console.error("Cannot trigger debug transition: questions_data is missing or empty. Check constructor.");
             return;
         }

         console.log("Generating random answers for debug transition...");
         const randomAnswers = this.questions_data.map(q => {
             if (q.options && q.options.length > 0) {
                 const randomIndex = Math.floor(Math.random() * q.options.length);
                 return q.options[randomIndex];
             } else {
                 return "Debug Fallback Answer";
             }
         });

         console.log("Debug Random Answers:", randomAnswers);
         this.triggerDungeonTransition(randomAnswers); // Call WITH args
     }

     // Add helper methods for hiding UI elements if they don't exist
     hideOptions() {
         const optionsContainer = document.getElementById('dialogue-options');
         if (optionsContainer) optionsContainer.replaceChildren(); // Clear content
     }

     hideFinalButton() {
         const finalButton = document.getElementById('final-button-id');
         if (finalButton) finalButton.classList.add('hidden'); // Or remove it
     }

    // --- Add back missing helper methods for post-animation flow ---
    handlePostWalkAdvance() {
        console.log("Handling post-walk advance...");
        if (!this.isWaitingPostWalkInput) return;
        this.isWaitingPostWalkInput = false;

        // Type the ellipsis and set up the next state in the callback
        this.typeDialogueLine("...", () => {
            console.log("Ellipsis typed. Waiting for post-ellipsis advance.");
            this.isWaitingPostEllipsisInput = true; // Set flag for the *next* click
        }, 150, 'ellipsis');
    }

    handlePostEllipsisAdvance() {
        console.log("Handling post-ellipsis advance...");
        if (!this.isWaitingPostEllipsisInput) return;
        this.isWaitingPostEllipsisInput = false;

        // Clear the ellipsis text
        if(this.dialogueTextElement) this.dialogueTextElement.textContent = '';

        // Start the next animation sequence
        this._startLookAroundAnimation(); // Assuming this method exists or needs creating
    }

    // Placeholder or ensure this method exists for the look-around animation
     _startLookAroundAnimation() {
         console.log("Starting look around animation...");
         this.lookAroundAnimation.active = true;
         this.lookAroundAnimation.startTime = this.sceneManager.clock.getElapsedTime();
         this.lookAroundAnimation.phase = 0; // Reset animation phase
     }
     // --- End missing methods ---

    // Add back missing animation start method
    _startFireDanceAnimation() {
        if (this.fireGroup) {
            console.log("Starting fire dance animation.");
            this.fireDanceAnimation.active = true;
            this.fireDanceAnimation.startTime = this.sceneManager.clock.getElapsedTime();
        }
    }

    // --- Method to SHOW Video ---
    showFlickerVideo() {
        console.log("[showFlickerVideo] Function called.");
        // Ensure correct video element is visible and others are hidden
        if (this.introVideo) this.introVideo.style.display = 'block';
        if (this.rebornVideo) this.rebornVideo.style.display = 'none';

        if (!this.videoOverlay || !this.introVideo) {
            console.error("[showFlickerVideo] Video overlay or introVideo elements NOT found! Skipping video.");
            // If video can't play, immediately proceed to the next line
            this.nextFireRevealLine();
            return;
        }
        console.log("[showFlickerVideo] Video elements found.", { overlay: this.videoOverlay, video: this.introVideo });

        console.log("[showFlickerVideo] Showing video overlay...");
        // Ensure dialogue is hidden behind video
        if (this.dialogueContainer) {
            console.log("[showFlickerVideo] Setting dialogue zIndex to 999.");
            this.dialogueContainer.style.zIndex = '999';
        } else {
            console.warn("[showFlickerVideo] Dialogue container not found for zIndex adjustment.");
        }

        this.videoOverlay.classList.add('visible');
        this.videoOverlay.offsetHeight; // Trigger reflow
        this.videoOverlay.classList.add('fading-in');
        console.log("[showFlickerVideo] Overlay classes added: visible, fading-in.");

        // Remove the 'ended' listener logic
        /*
        const onVideoEnd = () => { ... };
        this.introVideo.addEventListener('ended', onVideoEnd, { once: true });
        */

        console.log("[showFlickerVideo] Attempting to play intro video (looping)...");
        this.introVideo.currentTime = 0;
        const playPromise = this.introVideo.play();

        if (playPromise !== undefined) {
            playPromise.then(_ => {
                 // Video started playing, now wait for input to hide it
                 console.log("[showFlickerVideo] play().then: Video playback started successfully. Setting waitingForInputAfterVideo = true and recording start time.");
                 this.waitingForInputAfterVideo = true;
                 this.flickerVideoStartTime = performance.now(); // <<< Record start time
                 // <<< Play Flicker Laugh Sound >>>
                 console.log("[showFlickerVideo] Playing flicker laugh sound...");
                 this.audioManager?.playSound('flicker_laugh'); // <<< Changed key back to underscore
             }).catch(error => {
                console.error("[showFlickerVideo] play().catch: Video auto-play failed:", error);
                // Fallback: Hide overlay, immediately proceed to next line
                this.videoOverlay.classList.remove('visible', 'fading-in');
                if (this.dialogueContainer) this.dialogueContainer.style.zIndex = '1000';
                console.log("[showFlickerVideo] play().catch: Calling nextFireRevealLine() as fallback.");
                this.nextFireRevealLine(); // Proceed dialogue
            });
        } else {
            // If play() doesn't return a promise (older browsers?), assume it failed
             console.error("[showFlickerVideo] Video play() did not return a promise. Skipping video.");
             this.videoOverlay.classList.remove('visible', 'fading-in');
             if (this.dialogueContainer) this.dialogueContainer.style.zIndex = '1000';
             console.log("[showFlickerVideo] No promise: Calling nextFireRevealLine() as fallback.");
             this.nextFireRevealLine(); // Proceed dialogue
        }
    }

    // --- New Method to HIDE Video ---
    hideFlickerVideo() {
        console.log("[hideFlickerVideo] Hiding Flicker video...");
        this.flickerVideoStartTime = null; // <<< Reset start time
        if (!this.videoOverlay || !this.introVideo) {
            console.error("Video overlay or introVideo elements not found during hide! Proceeding dialogue anyway.");
            // Directly type the next line as fallback (Now line 1)
             if (fireRevealDialogue.length > 1) { // <<< Check for index 1
                 this.typeDialogueLine(fireRevealDialogue[1], this.nextFireRevealLine.bind(this)); // <<< Type line 1
             } else {
                 this.startQuestionnaire(); // Or whatever should happen after line 0
             }
            return;
        }

        console.log("Fading out video overlay...");
        this.videoOverlay.classList.remove('fading-in');

        // Wait for fade-out transition before hiding, pausing, and typing next line
        setTimeout(() => {
            console.log("Video overlay faded out. Hiding flicker video, pausing, and advancing dialogue.");
            this.videoOverlay.classList.remove('visible');
            if (this.introVideo) {
                 this.introVideo.pause(); // Pause the looping video
                 this.introVideo.style.display = 'none'; // Hide it
            }
            // Restore dialogue z-index
            if (this.dialogueContainer) this.dialogueContainer.style.zIndex = '1000';

            // --- Directly type the next line (index 1) ---
            console.log("[hideFlickerVideo] Explicitly typing line index 1."); // <<< Changed Log
            if (fireRevealDialogue.length > 1) { // <<< Check for index 1
                this.typeDialogueLine(fireRevealDialogue[1], this.nextFireRevealLine.bind(this)); // <<< Type line 1
            } else {
                 console.warn("[hideFlickerVideo] fireRevealDialogue doesn't have index 1?");
                 this.nextFireRevealLine(); // Fallback to normal advance
            }
        }, 500); // Match the CSS opacity transition duration
    }

    // --- New Method to Play Reborn Video ---
    playRebornVideo(stateParams) {
        console.log("--- [playRebornVideo] START --- ");

        // Log element finding
        console.log("[playRebornVideo] Finding elements:", {
            overlay: this.videoOverlay,
            intro: this.introVideo,
            reborn: this.rebornVideo
        });

        // Ensure correct video element is visible and others are hidden
        if (this.introVideo) {
            console.log("[playRebornVideo] Setting introVideo display to 'none'.");
            this.introVideo.style.display = 'none';
        } else {
            console.warn("[playRebornVideo] introVideo element NOT found for hiding.");
        }

        if (this.rebornVideo) {
             console.log("[playRebornVideo] Setting rebornVideo display to 'block'.");
            this.rebornVideo.style.display = 'block';
        } else {
            console.error("[playRebornVideo] rebornVideo element not found! Cannot play.");
            // Fallback: Directly change state
            this.sceneManager.changeState(STATE.DUNGEON, stateParams);
            return;
        }

        if (!this.videoOverlay) {
            console.error("[playRebornVideo] videoOverlay element not found! Cannot play.");
             // Fallback: Directly change state
             this.sceneManager.changeState(STATE.DUNGEON, stateParams);
             return;
        }

        console.log("[playRebornVideo] Showing video overlay (adding classes visible, fading-in)...");
        this.videoOverlay.classList.add('visible');
        this.videoOverlay.offsetHeight; // Trigger reflow
        this.videoOverlay.classList.add('fading-in');

        // Add event listener for when this specific video ends
        const onRebornVideoEnd = () => {
            console.log("--- [onRebornVideoEnd] START --- ");
            console.log("[playRebornVideo] Reborn video ended.");
            if (this.rebornVideo) {
                this.rebornVideo.removeEventListener('ended', onRebornVideoEnd);
                console.log("[onRebornVideoEnd] Removed 'ended' listener.");
            } else {
                 console.warn("[onRebornVideoEnd setTimeout] rebornVideo not found for listener removal?");
            }

            // Fade out video overlay
            console.log("[onRebornVideoEnd setTimeout] Fading out video overlay (removing fading-in class)...");
            if (this.videoOverlay) {
                 this.videoOverlay.classList.remove('fading-in');
            } else {
                 console.warn("[onRebornVideoEnd setTimeout] videoOverlay not found for fade out.");
            }

            // Wait for fade-out transition before changing state
            setTimeout(() => {
                console.log("--- [onRebornVideoEnd setTimeout] START --- ");
                console.log("[playRebornVideo] Video overlay faded out. Changing state to Dungeon.");
                 if (this.videoOverlay) {
                     console.log("[onRebornVideoEnd setTimeout] Hiding overlay (removing visible class).");
                     this.videoOverlay.classList.remove('visible');
                 } else {
                     console.warn("[onRebornVideoEnd setTimeout] videoOverlay not found for hiding.");
                 }
                 if (this.rebornVideo) {
                     console.log("[onRebornVideoEnd setTimeout] Hiding rebornVideo (display: none).");
                     this.rebornVideo.style.display = 'none'; // Hide video
                 } else {
                     console.warn("[onRebornVideoEnd setTimeout] rebornVideo not found for hiding.");
                 }

                // Now proceed to the Dungeon state with the pre-generated data
                 console.log("[onRebornVideoEnd setTimeout] Calling sceneManager.changeState(STATE.DUNGEON) with params...");
                this.sceneManager.changeState(STATE.DUNGEON, stateParams);
                 console.log("--- [onRebornVideoEnd setTimeout] END --- ");
            }, 500); // Match the CSS opacity transition duration
             console.log("--- [onRebornVideoEnd] END (setTimeout scheduled) --- ");
        };
        console.log("[playRebornVideo] Adding 'ended' event listener to rebornVideo.");
        this.rebornVideo.addEventListener('ended', onRebornVideoEnd, { once: true });

        // Attempt to play the video
        console.log("[playRebornVideo] Setting rebornVideo currentTime = 0.");
        this.rebornVideo.currentTime = 0; // Ensure video starts from the beginning
        console.log("[playRebornVideo] Calling rebornVideo.play()...");
        const playPromise = this.rebornVideo.play();

        if (playPromise !== undefined) {
            playPromise.then(_ => {
                 console.log("[playRebornVideo] play().then: Playback started or will start soon.");
                 // <<< Play Creepy Noise Sound >>>
                 console.log("[playRebornVideo] Playing creepy noise sound...");
                 this.audioManager?.playSound('creepy_noise');
             }).catch(error => {
                console.error("[playRebornVideo] play().catch: Video auto-play failed:", error);
                // Fallback: Hide overlay, directly change state
                if (this.rebornVideo) this.rebornVideo.removeEventListener('ended', onRebornVideoEnd);
                if (this.videoOverlay) this.videoOverlay.classList.remove('visible', 'fading-in');
                if (this.rebornVideo) this.rebornVideo.style.display = 'none';
                 console.log("[playRebornVideo] play().catch: Calling sceneManager.changeState as fallback with params.");
                this.sceneManager.changeState(STATE.DUNGEON, stateParams);
            });
        } else {
            console.error("[playRebornVideo] video.play() did not return a promise. Skipping video.");
            // Fallback: Directly change state if play API is unusable with params
            console.log("[playRebornVideo] No promise: Calling sceneManager.changeState as fallback with params.");
            this.sceneManager.changeState(STATE.DUNGEON, stateParams);
        }
         console.log("--- [playRebornVideo] END --- (play initiated, waiting for ended event) --- ");
    }

    finishQuestionnaire(debugAnswersArray = null) { // <<< Accept optional debug answers
        console.log("--- [finishQuestionnaire] START ---");

        // Determine which answers to use
        let answersToUse;
        if (debugAnswersArray) {
            answersToUse = debugAnswersArray;
            console.log("Using DEBUG answers:", answersToUse);
        } else {
            answersToUse = this.userAnswers.map(item => item.answer);
            console.log("Using collected user answers:", answersToUse);
        }

        // Ensure answers are in the correct format (array of strings)
        if (!Array.isArray(answersToUse) || answersToUse.some(item => typeof item !== 'string')) {
             console.error("Cannot proceed: Invalid answer format provided to finishQuestionnaire.", answersToUse);
             return; // Stop if format is wrong
        }

        // console.log("All questions answered. User answers:", this.userAnswers);
        if (this.dialogueContainer) {
            console.log("[finishQuestionnaire] Hiding dialogue container.");
            this.dialogueContainer.classList.add('hidden');
        }

        // --- Pre-generate Dungeon Assets ---
        console.log("[finishQuestionnaire] Pre-generating dungeon data...");
        let floorLayout = null;
        let playerMesh = null;
        try {
            const generator = new DungeonGenerator();
            floorLayout = generator.generateLayout(); // Generate layout
            playerMesh = createElementalPlayerModel(); // Create player model
            console.log("[finishQuestionnaire] Dungeon data pre-generation complete.");
        } catch (error) {
            console.error("[finishQuestionnaire] Error during pre-generation:", error);
            // Handle error - maybe prevent transition or show error message?
            // For now, we'll log it and proceed, DungeonHandler might fail later.
        }
        // -----------------------------------

        // Store final answers for passing to next state
        const finalStateParams = {
             userAnswers: answersToUse, // <<< Use the determined answers
             floorLayout: floorLayout, // Pass pre-generated layout
             playerMesh: playerMesh   // Pass pre-generated player mesh
        };

        // --- Instead of direct state change, play reborn video ---
        console.log("[finishQuestionnaire] Calling playRebornVideo()..."); // <<< Log
        // Pass the prepared params to the video end handler -> changeState call
        this.playRebornVideo(finalStateParams);
        console.log("--- [finishQuestionnaire] END --- (playRebornVideo is asynchronous)"); // <<< Log End
    }

    // --- NEW: Mobile Dialogue Control Methods ---
    navigateDialogue(direction) {
        // Only navigate if options are currently displayed
        const options = this.dialogueOptionsContainer?.querySelectorAll('.dialogue-option-button');
        if (options && options.length > 0) {
            if (direction === 'up') {
                this.highlightedOptionIndex = (this.highlightedOptionIndex - 1 + options.length) % options.length;
            } else if (direction === 'down') {
                this.highlightedOptionIndex = (this.highlightedOptionIndex + 1) % options.length;
            }
            this._updateHighlight();
        } else {
            // console.log("Navigate called but no options visible.");
        }
    }

    confirmDialogue() {
        // Check if options are visible and one is highlighted
        const options = this.dialogueOptionsContainer?.querySelectorAll('.dialogue-option-button');
        if (options && options.length > 0 && this.highlightedOptionIndex >= 0) {
            // Simulate clicking the highlighted button
            options[this.highlightedOptionIndex].click();
        } else {
            // If no options, simulate the general advance click
            this.onClickAdvance();
        }
    }
    // --- END Mobile Dialogue Control Methods ---

    // Add helper methods for hiding UI elements if they don't exist
    hideOptions() {
        const optionsContainer = document.getElementById('dialogue-options');
        if (optionsContainer) optionsContainer.replaceChildren(); // Clear content
    }

    // Ensure _triggerDebugDungeonTransition still calls correctly
    _triggerDebugDungeonTransition() {
        // Ensure questions_data is assigned in the constructor
        if (!this.questions_data || this.questions_data.length === 0) {
            console.error("Cannot trigger debug transition: questions_data is missing or empty. Check constructor.");
            return;
        }

        console.log("DEBUG SHORTCUT: Generating random answers...");
        const randomAnswers = this.questions_data.map(q => {
            if (q.options && q.options.length > 0) {
                const randomIndex = Math.floor(Math.random() * q.options.length);
                return q.options[randomIndex];
            } else {
                return "Debug Fallback Answer";
            }
        });

        console.log("DEBUG SHORTCUT: Pre-generating dungeon data...");
        let floorLayout = null;
        let playerMesh = null;
        try {
            const generator = new DungeonGenerator();
            floorLayout = generator.generateLayout(); // Generate layout
            playerMesh = createElementalPlayerModel(); // Create player model
            console.log("DEBUG SHORTCUT: Pre-generation complete.");
        } catch (error) {
            console.error("DEBUG SHORTCUT: Error during pre-generation:", error);
            // Stop transition if pre-generation fails crucial step
            return;
        }

        console.log("DEBUG SHORTCUT: Bypassing video, starting fade transition...");

        // Package data
        const finalStateParams = {
            userAnswers: randomAnswers,
            floorLayout: floorLayout,
            playerMesh: playerMesh
        };

        // Clean up character creation listeners before starting fade
        this._removeEventListeners();
        // Optionally hide dialogue immediately
        if (this.dialogueContainer) this.dialogueContainer.classList.add('hidden');

        // Immediately trigger fade and state change
        this.sceneManager.startFade(() => {
            console.log("DEBUG SHORTCUT: Fade complete, changing state to Dungeon.");
            // The SceneManager cleanup for CharacterCreationHandler will run AFTER this callback
            // but BEFORE the fade-in completes, which is okay.
            this.sceneManager.changeState(STATE.DUNGEON, finalStateParams);
        });

        // --- REMOVE finishQuestionnaire call ---
        // console.log("Debug Random Answers:", randomAnswers);
        // this.finishQuestionnaire(randomAnswers);
        // ---------------------------------------
    }
}

export default CharacterCreationHandler;