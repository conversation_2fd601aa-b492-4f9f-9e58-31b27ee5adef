import * as THREE from 'three';
import { createSkeletonEnemyModel } from '../generators/prefabs/skeletonEnemy.js';
import { createBatEnemyModel } from '../generators/prefabs/batEnemy.js';
import { createZombieEnemyModel } from '../generators/prefabs/zombieEnemy.js';
import { createMagmaGolemEnemyModel } from '../generators/prefabs/magmaGolemEnemy.js';
import { createCatacombOverlordModel } from '../generators/prefabs/catacombOverlordBoss.js';

// --- AI Brain Types ---
export const AI_BRAIN_TYPES = {
    RANGED: 'ranged',
    MELEE: 'melee',
    MIRROR: 'mirror',
    ASSASSIN: 'assassin',
    FLEEING: 'fleeing',
    FLYING: 'flying',
    BOSS: 'boss'
};

// --- Enemy Type Definitions ---
export const ENEMY_TYPES = {
    SKELETON: 'skeleton',
    SKELETON_ARCHER: 'skeleton_archer',
    SK<PERSON>ETON_WARRIOR: 'skeleton_warrior',
    SKELETON_ASSASSIN: 'skeleton_assassin',
    BAT: 'bat',
    GHOST: 'ghost',
    SKELETON_BOSS: 'skeleton_boss',
    ZOMBIE: 'zombie',
    MAGMA_GOLEM: 'magma_golem',
    CATACOMBS_OVERLORD: 'catacombs_overlord'
};

const ENEMY_TYPES_DATA = {
    // --- Skeleton Archer (Ranged Combat AI) ---
    skeleton_archer: {
        // Visuals
        modelPrefab: createSkeletonEnemyModel,
        size: 0.8,
        // Base Stats
        health: 30,
        baseSpeed: 2.5, // Increased from 1.5 for faster movement
        speedVariation: 0.3, // Increased from 0.2 for more varied movement
        mass: 1.0, // For knockback calculations
        // AI Parameters
        aiType: AI_BRAIN_TYPES.RANGED,
        difficulty: 1, // Default difficulty (1-5)
        attackCooldown: 1.2, // Increased from 0.4 to make skeletons shoot less frequently
        preferredRange: 8.0, // Good distance for shooting
        moveAwayRange: 3.0, // Distance at which to start moving away
        maxRange: 30.0, // Maximum detection and shooting range
        // Projectile Info
        projectileType: 'arrow',
        projectileDamage: 1, // Reduced from 5 to 1
        projectileSpeed: 7.0, // Reduced from 15.0 to make arrows slower and more dodgeable
        projectileRange: 30.0, // Increased from 25.0 to match detection range
        projectileSize: 0.05,
        // Dodge Parameters
        canDodge: true,
        dodgeChance: 0.2,
        dodgeCooldown: 3.0,
        dodgeDuration: 0.5,
        dodgeTriggerDistance: 5.0
    },

    // --- Skeleton Warrior (Melee Combat AI) ---
    skeleton_warrior: {
        // Visuals
        modelPrefab: createSkeletonEnemyModel,
        size: 0.8,
        // Base Stats
        health: 45,
        baseSpeed: 2.8, // Increased from 1.8 for faster movement
        speedVariation: 0.25, // Increased from 0.15 for more varied movement
        mass: 1.2, // Heavier than archer
        // AI Parameters
        aiType: AI_BRAIN_TYPES.MELEE,
        difficulty: 1, // Default difficulty (1-5)
        attackCooldown: 1.0, // Reduced from 1.5 for faster attacks
        preferredRange: 2.0,
        attackRange: 2.5,
        chargeRange: 6.0,
        // Melee Attack Info
        meleeDamage: 1, // Reduced from 8 to 1
        // Block Parameters
        canBlock: true,
        blockChance: 0.3,
        blockCooldown: 2.0,
        blockDuration: 1.0
    },

    // --- Skeleton Assassin (Assassin Combat AI) ---
    skeleton_assassin: {
        // Visuals
        modelPrefab: createSkeletonEnemyModel,
        size: 0.8,
        // Base Stats
        health: 35,
        baseSpeed: 3.2, // Increased from 2.0 for faster movement
        speedVariation: 0.3, // Increased from 0.1 for more varied movement
        mass: 0.9, // Lighter than warrior
        // AI Parameters
        aiType: AI_BRAIN_TYPES.ASSASSIN,
        difficulty: 1, // Default difficulty (1-5)
        attackCooldown: 1.0, // Reduced from 1.8 for faster attacks
        preferredRange: 2.0,
        attackRange: 2.5,
        stalkRange: 10.0,
        // Melee Attack Info
        meleeDamage: 1, // Reduced from 6 to 1
        backstabMultiplier: 1.0, // Reduced from 2.0 to 1.0 (no multiplier)
        // Stealth Parameters
        stealthLevel: 0.7
    },

    // --- Bat (Flying Combat AI) ---
    bat: {
        // Visuals
        modelPrefab: createBatEnemyModel,
        size: 0.5,
        // Base Stats
        health: 20,
        baseSpeed: 6.0, // Extremely fast base speed
        speedVariation: 0.1, // Less variation for more consistent speed
        mass: 0.5, // Very light
        // AI Parameters
        aiType: AI_BRAIN_TYPES.FLYING,
        difficulty: 1, // Default difficulty (1-5)
        attackCooldown: 0.8, // Extremely short cooldown for very frequent attacks
        preferredRange: 1.5, // Extremely close preferred range
        attackRange: 4.5, // Increased from 2.0 to 4.5 to match the new hitbox radius
        // Flying Parameters
        minHoverHeight: 1.2, // Very low minimum height
        maxHoverHeight: 2.5, // Lower maximum height to stay closer to player
        // Melee Attack Info
        meleeDamage: 1, // Reduced from 4 to 1
        // Swooping Parameters
        swoopChance: 0.9, // Almost always swoop when possible
        swoopCooldown: 1.5, // Very short cooldown between swoops
        // Animation Parameters
        animationStates: {
            idle: 'flying',
            moving: 'flying',
            attacking: 'biting',
            swooping: 'swooping'
        }
    },

    // --- Ghost (Fleeing Combat AI) ---
    ghost: {
        // Visuals (placeholder, need to create ghost model)
        modelPrefab: null, // TODO: Create ghost model
        size: 0.7,
        // Base Stats
        health: 15,
        baseSpeed: 1.7,
        speedVariation: 0.25,
        mass: 0.3, // Almost no mass
        // AI Parameters
        aiType: AI_BRAIN_TYPES.FLEEING,
        difficulty: 1, // Default difficulty (1-5)
        fleeThreshold: 8.0,
        panicThreshold: 5.0,
        safeDistance: 12.0
    },

    // --- Skeleton Boss (Boss Combat AI) ---
    skeleton_boss: {
        // Visuals (placeholder, need to create boss model)
        modelPrefab: createSkeletonEnemyModel, // TODO: Create boss model
        size: 1.5, // Larger than normal skeleton
        // Base Stats
        health: 150,
        baseSpeed: 1.6,
        speedVariation: 0.1,
        mass: 2.0, // Heavy
        // AI Parameters
        aiType: AI_BRAIN_TYPES.BOSS,
        difficulty: 3, // Higher default difficulty
        attackCooldown: 1.5,
        // Projectile Info
        projectileType: 'shadow_bolt',
        projectileDamage: 1, // Reduced from 10 to 1
        projectileSpeed: 8.0,
        projectileRange: 15.0,
        projectileSize: 0.3,
        // Melee Attack Info
        meleeDamage: 1, // Reduced from 12 to 1
        // Special Attack Info
        specialAttackCooldown: 15.0,
        specialAttackDamage: 1 // Reduced from 20 to 1
    },

    // --- Catacombs Overlord (Music-Reactive Boss) ---
    catacombs_overlord: {
        // Visuals
        modelPrefab: createCatacombOverlordModel, // Using custom overlord model
        size: 8.0, // Reduced from 12.5 to make the boss a bit smaller
        // Base Stats
        health: 300, // Increased health for stronger boss
        baseSpeed: 1.8,
        speedVariation: 0.1,
        mass: 3.0, // Extremely heavy
        // AI Parameters
        aiType: AI_BRAIN_TYPES.BOSS,
        difficulty: 5, // Maximum difficulty
        attackCooldown: 1.2,
        // Music-reactive flag
        musicReactive: true,
        // Projectile Info
        projectileType: 'shadow_bolt', // Default projectile type
        projectileDamage: 1,
        projectileSpeed: 8.0,
        projectileRange: 20.0,
        projectileSize: 0.3,
        // Melee Attack Info
        meleeDamage: 1,
        // Special Attack Info
        specialAttackCooldown: 10.0, // Shorter cooldown for more frequent special attacks
        specialAttackDamage: 1
    },

    // --- Legacy skeleton type for backward compatibility ---
    skeleton: {
        // Visuals
        modelPrefab: createSkeletonEnemyModel,
        size: 0.8,
        // Base Stats
        health: 30,
        baseSpeed: 1.5,
        speedVariation: 0.2,
        mass: 1.0,
        // AI Parameters (maps to skeleton_archer)
        aiType: AI_BRAIN_TYPES.RANGED,
        difficulty: 1,
        attackCooldown: 1.2, // Increased from 0.4 to make skeletons shoot less frequently
        preferredRange: 8.0, // Good distance for shooting
        moveAwayRange: 3.0, // Distance at which to start moving away
        maxRange: 30.0, // Maximum detection and shooting range
        // Projectile Info
        projectileType: 'arrow',
        projectileDamage: 1, // Reduced from 5 to 1
        projectileSpeed: 7.0, // Reduced from 15.0 to make arrows slower and more dodgeable
        projectileRange: 30.0, // Increased from 25.0 to match detection range
        projectileSize: 0.05
    },

    // --- Zombie (Melee Combat AI) ---
    zombie: {
        // Visuals
        modelPrefab: createZombieEnemyModel,
        size: 1.5, // Larger size for the new zombie model
        // Base Stats
        health: 60, // Increased health for more durability
        baseSpeed: 1.8, // Slower than before - shambling zombie
        speedVariation: 0.3, // More variation in speed
        mass: 1.8, // Much heavier than before
        // AI Parameters
        aiType: AI_BRAIN_TYPES.MELEE,
        difficulty: 1, // Default difficulty (1-5)
        attackCooldown: 1.5, // Slower attacks
        preferredRange: 1.5, // Very close preferred range
        attackRange: 3.0, // Wider attack range
        chargeRange: 8.0, // Longer charge range
        // Melee Attack Info
        meleeDamage: 1, // 1 damage as per user preference
        // Animation Parameters
        animationStates: {
            idle: 'idle',
            moving: 'walking',
            attacking: 'attacking'
        }
    },

    // --- Magma Golem (Melee Combat AI) ---
    magma_golem: {
        // Visuals
        modelPrefab: createMagmaGolemEnemyModel,
        size: 2.5, // Much larger size to make it more visible
        // Base Stats
        health: 80, // High health for a tank-like enemy
        baseSpeed: 1.5, // Slow but steady
        speedVariation: 0.2, // Some variation in speed
        mass: 2.5, // Very heavy
        // AI Parameters
        aiType: AI_BRAIN_TYPES.MELEE,
        difficulty: 2, // Slightly higher difficulty
        attackCooldown: 2.0, // Slow but powerful attacks
        preferredRange: 2.0, // Close preferred range
        attackRange: 4.0, // Wide attack range
        chargeRange: 7.0, // Good charge range
        // Melee Attack Info
        meleeDamage: 1, // 1 damage as per user preference
        // Animation Parameters
        animationStates: {
            idle: 'idle',
            moving: 'walking',
            attacking: 'attacking'
        }
    }
};

// --- Helper to get enemy data ---
// Ensures we don't modify the original definition object
export function getEnemyData(enemyType) {
    const data = ENEMY_TYPES_DATA[enemyType];
    if (!data) {
        console.warn(`Enemy type "${enemyType}" not found!`);
        return null;
    }
    // Return a copy to prevent accidental modification of the definition
    // Note: This is a shallow copy, geometry/material are still references
    return { ...data };
}

// Pre-create geometry/material instances if they are shared often
// (Currently simple, but could use functions from voxelPrefabs later)
// REMOVED OLD PRE-CREATION BLOCK FOR SKELETON BOX

// Make the main definitions read-only (optional, good practice)
Object.freeze(ENEMY_TYPES_DATA);